#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试反馈数据
"""

import sqlite3
import random
from datetime import datetime, timedelta

def generate_test_feedback_data():
    """生成测试反馈数据"""
    conn = sqlite3.connect('scholarship_data.db')
    cursor = conn.cursor()
    
    try:
        # 获取现有用户ID
        cursor.execute('SELECT id FROM users WHERE role = "student" LIMIT 10')
        user_ids = [row[0] for row in cursor.fetchall()]
        
        if not user_ids:
            print("没有找到学生用户，请先创建用户")
            return
        
        # 反馈类型和标题模板
        feedback_types = ['bug_report', 'feature_request', 'other']
        
        bug_titles = [
            '登录页面无法正常显示',
            '成绩查询功能报错',
            '排名页面加载缓慢',
            '导出功能不工作',
            '密码修改失败',
            '图表显示异常',
            '分页功能有问题',
            '搜索结果不准确'
        ]
        
        feature_titles = [
            '希望增加成绩趋势图',
            '建议添加消息通知功能',
            '希望支持批量操作',
            '建议优化移动端体验',
            '希望增加数据导入功能',
            '建议添加个人统计页面',
            '希望支持多语言',
            '建议增加暗色主题'
        ]
        
        other_titles = [
            '使用体验很好',
            '界面设计建议',
            '功能使用咨询',
            '数据准确性问题',
            '系统使用培训需求',
            '账户权限问题',
            '操作流程建议',
            '系统性能反馈'
        ]
        
        bug_contents = [
            '在Chrome浏览器中打开登录页面时，页面布局错乱，无法正常输入用户名和密码。',
            '点击成绩查询按钮后，系统显示500错误，无法查看成绩信息。',
            '排名页面加载时间超过30秒，严重影响使用体验。',
            '点击导出Excel按钮后，没有任何反应，无法下载文件。',
            '在个人设置页面修改密码时，提示"密码修改失败"，但没有具体错误信息。',
            '首页的统计图表显示数据异常，部分数据显示为NaN。',
            '在数据列表页面，分页功能无法正常工作，点击下一页没有反应。',
            '使用搜索功能时，搜索结果与实际数据不符，可能存在索引问题。'
        ]
        
        feature_contents = [
            '希望在学生个人页面增加成绩趋势图，可以直观看到各学期成绩变化。',
            '建议增加消息通知功能，当有重要信息更新时能及时通知用户。',
            '希望在管理页面支持批量操作，如批量删除、批量修改状态等。',
            '当前系统在手机上使用体验不佳，希望能优化移动端界面。',
            '希望增加Excel数据导入功能，方便批量更新学生信息。',
            '建议为每个学生增加个人统计页面，显示详细的学业分析。',
            '希望系统能支持多语言，方便国际学生使用。',
            '建议增加暗色主题选项，在夜间使用时更加舒适。'
        ]
        
        other_contents = [
            '整体使用体验很好，界面简洁美观，功能齐全。',
            '建议调整一下颜色搭配，使界面更加协调。',
            '请问如何查看历史成绩记录？在哪个页面可以找到？',
            '发现部分学生的成绩数据可能有误，建议核实一下。',
            '希望能提供系统使用培训，帮助老师更好地使用系统。',
            '我的账户权限似乎有问题，无法访问某些功能页面。',
            '建议简化一些操作流程，减少不必要的步骤。',
            '系统整体运行稳定，但偶尔会有卡顿现象。'
        ]
        
        statuses = ['pending', 'viewed', 'in_progress', 'resolved', 'closed']
        
        # 生成30条测试反馈
        for i in range(30):
            feedback_type = random.choice(feedback_types)
            user_id = random.choice(user_ids)
            
            if feedback_type == 'bug_report':
                title = random.choice(bug_titles)
                content = random.choice(bug_contents)
            elif feedback_type == 'feature_request':
                title = random.choice(feature_titles)
                content = random.choice(feature_contents)
            else:
                title = random.choice(other_titles)
                content = random.choice(other_contents)
            
            status = random.choice(statuses)
            
            # 随机生成时间（近30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            created_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
            
            # 如果状态不是pending，则更新时间稍晚一些
            if status != 'pending':
                update_hours = random.randint(1, 48)
                updated_time = created_time + timedelta(hours=update_hours)
            else:
                updated_time = created_time
            
            # 管理员备注（部分反馈有）
            admin_notes = None
            if status in ['viewed', 'in_progress', 'resolved', 'closed'] and random.random() > 0.3:
                notes_templates = [
                    '已确认问题，正在处理中。',
                    '感谢反馈，我们会考虑您的建议。',
                    '问题已修复，请重新测试。',
                    '该功能已在开发计划中。',
                    '已转发给相关技术人员处理。',
                    '问题已解决，感谢您的反馈。'
                ]
                admin_notes = random.choice(notes_templates)
            
            cursor.execute('''
                INSERT INTO feedback (user_id, type, title, content, status, admin_notes, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, feedback_type, title, content, status, admin_notes,
                  created_time.strftime('%Y-%m-%d %H:%M:%S'),
                  updated_time.strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("成功生成 30 条测试反馈数据")
        
        # 显示统计信息
        cursor.execute('''
            SELECT type, COUNT(*) as count
            FROM feedback
            GROUP BY type
        ''')
        type_stats = cursor.fetchall()
        print("\n按类型统计:")
        for stat in type_stats:
            type_name = {'bug_report': '错误报告', 'feature_request': '功能建议', 'other': '其他'}
            print(f"  {type_name.get(stat[0], stat[0])}: {stat[1]} 条")
        
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM feedback
            GROUP BY status
        ''')
        status_stats = cursor.fetchall()
        print("\n按状态统计:")
        status_names = {
            'pending': '待处理',
            'viewed': '已查看',
            'in_progress': '处理中',
            'resolved': '已解决',
            'closed': '已关闭'
        }
        for stat in status_stats:
            print(f"  {status_names.get(stat[0], stat[0])}: {stat[1]} 条")
        
    except Exception as e:
        print(f"生成测试数据失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    generate_test_feedback_data()
