#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证路由
处理用户登录、注册、密码重置等功能
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from models.user import User
from models.database import log_user_action

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))
        
        if not username or not password:
            flash('请输入用户名和密码', 'error')
            return render_template('auth/login.html')
        
        user = User.get_by_username(username)
        if user and user.check_password(password):
            if not user.is_active:
                flash('账户已被禁用，请联系管理员', 'error')
                return render_template('auth/login.html')
            
            login_user(user, remember=remember)
            user.update_last_login()
            
            # 记录登录日志
            log_user_action(
                user.id, 
                'login', 
                f'用户登录: {username}',
                request.remote_addr,
                request.headers.get('User-Agent')
            )
            
            flash(f'欢迎回来，{user.full_name or user.username}！', 'success')
            
            # 重定向到原来要访问的页面或默认页面
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard.index'))
        else:
            flash('用户名或密码错误', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    # 记录登出日志
    log_user_action(
        current_user.id,
        'logout',
        f'用户登出: {current_user.username}',
        request.remote_addr,
        request.headers.get('User-Agent')
    )
    
    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册（仅管理员可访问）"""
    # 这里可以添加管理员权限检查
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        email = request.form.get('email', '').strip()
        full_name = request.form.get('full_name', '').strip()
        role = request.form.get('role', 'student')
        student_id = request.form.get('student_id', '').strip()
        
        # 验证输入
        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return render_template('auth/register.html')
        
        if len(password) < 6:
            flash('密码长度至少6位', 'error')
            return render_template('auth/register.html')
        
        # 检查用户名是否已存在
        if User.get_by_username(username):
            flash('用户名已存在', 'error')
            return render_template('auth/register.html')
        
        # 如果是学生，检查学号是否已绑定
        if role == 'student' and student_id:
            if User.get_by_student_id(student_id):
                flash('该学号已被绑定', 'error')
                return render_template('auth/register.html')
        
        # 创建用户
        user = User.create_user(
            username=username,
            password=password,
            email=email,
            full_name=full_name,
            role=role,
            student_id=student_id if role == 'student' else None
        )
        
        if user:
            flash('注册成功！', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('注册失败，请重试', 'error')
    
    return render_template('auth/register.html')

@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '').strip()
        new_password = request.form.get('new_password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()
        
        # 验证输入完整性
        if not current_password:
            flash('请输入当前密码', 'error')
            return render_template('auth/change_password.html')
        
        if not new_password:
            flash('请输入新密码', 'error')
            return render_template('auth/change_password.html')
        
        if not confirm_password:
            flash('请确认新密码', 'error')
            return render_template('auth/change_password.html')
        
        # 验证当前密码
        if not current_user.check_password(current_password):
            flash('当前密码错误，请重新输入', 'error')
            return render_template('auth/change_password.html')
        
        # 验证新密码强度
        if len(new_password) < 6:
            flash('新密码长度至少6位', 'error')
            return render_template('auth/change_password.html')
        
        if len(new_password) > 128:
            flash('新密码长度不能超过128位', 'error')
            return render_template('auth/change_password.html')
        
        # 检查新密码是否与当前密码相同
        if current_user.check_password(new_password):
            flash('新密码不能与当前密码相同', 'error')
            return render_template('auth/change_password.html')
        
        # 验证密码确认
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'error')
            return render_template('auth/change_password.html')
        
        try:
            # 更新密码
            current_user.set_password(new_password)
            if current_user.save():
                # 记录日志
                log_user_action(
                    current_user.id,
                    'password_changed',
                    '用户修改密码',
                    request.remote_addr,
                    request.headers.get('User-Agent')
                )
                
                flash('密码修改成功！为了您的账户安全，建议重新登录。', 'success')
                return redirect(url_for('auth.profile'))
            else:
                flash('密码修改失败，请重试', 'error')
        except Exception as e:
            flash('系统错误，密码修改失败', 'error')
            print(f"修改密码失败: {e}")
    
    return render_template('auth/change_password.html')

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """用户资料"""
    if request.method == 'POST':
        email = request.form.get('email', '').strip()
        full_name = request.form.get('full_name', '').strip()
        
        current_user.email = email
        current_user.full_name = full_name
        
        if current_user.save():
            # 记录日志
            log_user_action(
                current_user.id,
                'profile_updated',
                '用户更新个人资料',
                request.remote_addr,
                request.headers.get('User-Agent')
            )
            
            flash('个人资料更新成功！', 'success')
        else:
            flash('更新失败，请重试', 'error')
    
    return render_template('auth/profile.html')
