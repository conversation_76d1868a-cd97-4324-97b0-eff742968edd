{% extends "base.html" %}

{% block title %}提交反馈{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-comment-dots me-2"></i>提交反馈
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">首页</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('feedback.my_feedback') }}">我的反馈</a></li>
                        <li class="breadcrumb-item active">提交反馈</li>
                    </ol>
                </nav>
            </div>

            <!-- 反馈提交表单 -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>反馈信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="feedbackForm">
                                <!-- 反馈类型 -->
                                <div class="mb-3">
                                    <label for="type" class="form-label">反馈类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="type" name="type" required>
                                        <option value="bug_report">错误报告</option>
                                        <option value="feature_request">功能建议</option>
                                        <option value="other" selected>其他</option>
                                    </select>
                                    <div class="form-text">请选择最符合您反馈内容的类型</div>
                                </div>

                                <!-- 反馈标题 -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">反馈标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           placeholder="请简要描述您的反馈内容" maxlength="100" required>
                                    <div class="form-text">标题应简洁明了，不超过100个字符</div>
                                </div>

                                <!-- 详细描述 -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">详细描述 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="content" name="content" rows="6" 
                                              placeholder="请详细描述您遇到的问题或建议..." required></textarea>
                                    <div class="form-text">
                                        请尽可能详细地描述问题或建议，包括：
                                        <ul class="mb-0 mt-1">
                                            <li>问题发生的具体步骤</li>
                                            <li>期望的结果和实际结果</li>
                                            <li>使用的浏览器和操作系统</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- 附件上传 -->
                                <div class="mb-4">
                                    <label for="attachment" class="form-label">上传截图（可选）</label>
                                    <input type="file" class="form-control" id="attachment" name="attachment" 
                                           accept="image/*">
                                    <div class="form-text">
                                        支持 PNG、JPG、JPEG、GIF、BMP、WebP 格式，最大 16MB
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('feedback.my_feedback') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>返回
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-paper-plane me-2"></i>提交反馈
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 反馈指南 -->
            <div class="row justify-content-center mt-4">
                <div class="col-lg-8">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>反馈指南
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6 class="text-primary">错误报告</h6>
                                    <p class="small text-muted">
                                        报告系统bug、功能异常、页面错误等技术问题
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-primary">功能建议</h6>
                                    <p class="small text-muted">
                                        提出新功能需求、改进建议、用户体验优化等
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-primary">其他反馈</h6>
                                    <p class="small text-muted">
                                        使用咨询、意见建议、内容问题等其他反馈
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('feedbackForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // 表单提交处理
    form.addEventListener('submit', function(e) {
        // 禁用提交按钮防止重复提交
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
        
        // 如果验证失败，重新启用按钮
        setTimeout(function() {
            if (!form.checkValidity()) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>提交反馈';
            }
        }, 100);
    });
    
    // 文件大小验证
    document.getElementById('attachment').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.size > 16 * 1024 * 1024) {
            alert('文件大小不能超过 16MB');
            e.target.value = '';
        }
    });
    
    // 字符计数
    const titleInput = document.getElementById('title');
    const contentTextarea = document.getElementById('content');
    
    function updateCharCount(element, maxLength) {
        const current = element.value.length;
        const remaining = maxLength - current;
        let countElement = element.parentNode.querySelector('.char-count');
        
        if (!countElement) {
            countElement = document.createElement('div');
            countElement.className = 'char-count form-text text-end';
            element.parentNode.appendChild(countElement);
        }
        
        countElement.textContent = `${current}/${maxLength}`;
        countElement.className = `char-count form-text text-end ${remaining < 10 ? 'text-warning' : ''}`;
    }
    
    titleInput.addEventListener('input', function() {
        updateCharCount(this, 100);
    });
    
    contentTextarea.addEventListener('input', function() {
        updateCharCount(this, 1000);
    });
});
</script>
{% endblock %}
