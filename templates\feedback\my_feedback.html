{% extends "base.html" %}

{% block title %}我的反馈{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-comments me-2"></i>我的反馈
                </h2>
                <div>
                    <a href="{{ url_for('feedback.submit') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>提交新反馈
                    </a>
                </div>
            </div>

            <!-- 筛选器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态筛选</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>待处理</option>
                                <option value="viewed" {% if current_status == 'viewed' %}selected{% endif %}>已查看</option>
                                <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>处理中</option>
                                <option value="resolved" {% if current_status == 'resolved' %}selected{% endif %}>已解决</option>
                                <option value="closed" {% if current_status == 'closed' %}selected{% endif %}>已关闭</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">类型筛选</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">全部类型</option>
                                <option value="bug_report" {% if current_type == 'bug_report' %}selected{% endif %}>错误报告</option>
                                <option value="feature_request" {% if current_type == 'feature_request' %}selected{% endif %}>功能建议</option>
                                <option value="other" {% if current_type == 'other' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-filter me-2"></i>筛选
                            </button>
                            <a href="{{ url_for('feedback.my_feedback') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>重置
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 反馈列表 -->
            {% if feedback_list %}
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>反馈记录
                        <span class="badge bg-primary ms-2">{{ pagination.total }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>标题</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>提交时间</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for feedback in feedback_list %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if feedback.attachment_path %}
                                            <i class="fas fa-paperclip text-muted me-2" title="包含附件"></i>
                                            {% endif %}
                                            <span class="fw-medium">{{ feedback.title }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        {% if feedback.type == 'bug_report' %}
                                        <span class="badge bg-danger">错误报告</span>
                                        {% elif feedback.type == 'feature_request' %}
                                        <span class="badge bg-success">功能建议</span>
                                        {% else %}
                                        <span class="badge bg-secondary">其他</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if feedback.status == 'pending' %}
                                        <span class="badge bg-warning">待处理</span>
                                        {% elif feedback.status == 'viewed' %}
                                        <span class="badge bg-info">已查看</span>
                                        {% elif feedback.status == 'in_progress' %}
                                        <span class="badge bg-primary">处理中</span>
                                        {% elif feedback.status == 'resolved' %}
                                        <span class="badge bg-success">已解决</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已关闭</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ feedback.created_at[:16] }}
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ feedback.updated_at[:16] }}
                                        </small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('feedback.detail', feedback_id=feedback.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            {% if pagination.total_pages > 1 %}
            <nav aria-label="反馈分页" class="mt-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
                            条，共 {{ pagination.total }} 条记录
                        </p>
                    </div>
                    <div class="col-md-6">
                        <ul class="pagination justify-content-end mb-0">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feedback.my_feedback', page=pagination.page-1, status=current_status, type=current_type) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page_num in range(1, pagination.total_pages + 1) %}
                                {% if page_num == pagination.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('feedback.my_feedback', page=page_num, status=current_status, type=current_type) }}">{{ page_num }}</a>
                                </li>
                                {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feedback.my_feedback', page=pagination.page+1, status=current_status, type=current_type) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </nav>
            {% endif %}

            {% else %}
            <!-- 空状态 -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无反馈记录</h5>
                    <p class="text-muted">您还没有提交过任何反馈</p>
                    <a href="{{ url_for('feedback.submit') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>提交第一个反馈
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
