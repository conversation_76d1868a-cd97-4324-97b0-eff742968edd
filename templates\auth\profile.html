<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 学生学业数据管理与分析平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
        
        .profile-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn-outline-primary {
            color: #4facfe;
            border-color: #4facfe;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: #4facfe;
            border-color: #4facfe;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            border: none;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #4facfe;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        .badge-role {
            padding: 0.5em 0.75em;
            border-radius: 1rem;
            font-size: 0.875em;
            font-weight: 500;
        }
        
        .badge-admin { background: #dc3545; color: white; }
        .badge-teacher { background: #fd7e14; color: white; }
        .badge-student { background: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h2 class="mb-0">{{ current_user.full_name or current_user.username }}</h2>
                        <p class="mb-0 mt-2">
                            <span class="badge badge-{{ current_user.role }}">
                                {% if current_user.role == 'admin' %}
                                    <i class="fas fa-crown me-1"></i>管理员
                                {% elif current_user.role == 'teacher' %}
                                    <i class="fas fa-chalkboard-teacher me-1"></i>教师
                                {% else %}
                                    <i class="fas fa-user-graduate me-1"></i>学生
                                {% endif %}
                            </span>
                        </p>
                    </div>
                    
                    <div class="profile-body">
                        <!-- Flash消息 -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <!-- 账户信息 -->
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>账户信息
                            </h5>
                            <div class="info-item">
                                <span class="info-label">用户名：</span>
                                <span class="info-value">{{ current_user.username }}</span>
                            </div>
                            {% if current_user.student_id %}
                            <div class="info-item">
                                <span class="info-label">学号：</span>
                                <span class="info-value">{{ current_user.student_id }}</span>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <span class="info-label">账户状态：</span>
                                <span class="info-value">
                                    {% if current_user.is_active %}
                                        <span class="text-success"><i class="fas fa-check-circle me-1"></i>正常</span>
                                    {% else %}
                                        <span class="text-danger"><i class="fas fa-times-circle me-1"></i>已禁用</span>
                                    {% endif %}
                                </span>
                            </div>
                            {% if current_user.last_login %}
                            <div class="info-item">
                                <span class="info-label">最后登录：</span>
                                <span class="info-value">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login else '从未登录' }}</span>
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- 编辑个人信息表单 -->
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-user me-1"></i>姓名
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="{{ current_user.full_name or '' }}" placeholder="请输入您的姓名">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>邮箱
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ current_user.email or '' }}" placeholder="请输入您的邮箱">
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                                <div class="d-grid gap-2 d-md-flex">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        保存信息
                                    </button>
                                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-key me-1"></i>
                                        修改密码
                                    </a>
                                </div>
                                <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    返回主页
                                </a>
                            </div>
                        </form>
                        
                        <!-- 安全提示 -->
                        <div class="mt-4">
                            <div class="alert alert-info d-flex align-items-center" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <div>
                                    <strong>安全提示：</strong>
                                    为了您的账户安全，建议定期更换密码，并避免在公共场所登录您的账户。
                                </div>
                            </div>
                        </div>
                        
                        {% if current_user.role == 'student' and current_user.student_id %}
                        <!-- 学生专用功能 -->
                        <div class="mt-3">
                            <div class="alert alert-success d-flex align-items-center" role="alert">
                                <i class="fas fa-graduation-cap me-2"></i>
                                <div>
                                    <strong>学生功能：</strong>
                                    您可以在<a href="{{ url_for('ranking.index') }}" class="alert-link">数据库</a>中查看您的成绩排名，
                                    在<a href="{{ url_for('analysis.student', student_id=current_user.student_id) }}" class="alert-link">个人分析</a>中查看详细的学业分析报告。
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if current_user.role == 'admin' or (current_user.role == 'teacher' and current_user.is_sub_admin) %}
                        <!-- 管理员/教师专用功能 -->
                        <div class="mt-3">
                            <div class="alert alert-warning d-flex align-items-center" role="alert">
                                <i class="fas fa-tools me-2"></i>
                                <div>
                                    <strong>管理功能：</strong>
                                    您拥有管理权限，可以访问
                                    {% if current_user.role == 'admin' %}
                                    <a href="{{ url_for('admin.index') }}" class="alert-link">管理面板</a>进行用户管理和系统配置。
                                    {% else %}
                                    数据分析和班级管理功能。
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            
            // 验证邮箱格式（如果填写了邮箱）
            if (email && !isValidEmail(email)) {
                e.preventDefault();
                alert('请输入有效的邮箱地址！');
                document.getElementById('email').focus();
                return false;
            }
        });
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // 显示保存成功的动画效果
        // 使用服务器端提示，客户端不需要额外处理
    </script>
</body>
</html> 