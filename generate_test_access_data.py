#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试访问数据脚本
用于为访问统计图表提供测试数据
"""

import sqlite3
import random
from datetime import datetime, timedelta
from models.database import get_db_connection

def generate_test_access_data():
    """生成近7天的测试访问数据"""
    
    # 连接数据库
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 清除现有的访问日志数据（仅测试数据）
        cursor.execute('DELETE FROM access_logs')
        
        # 生成近7天的数据
        today = datetime.now()
        
        # 模拟的用户ID列表（假设有一些用户）
        user_ids = [1, 2, 3, 4, 5, None]  # None表示未登录用户
        
        # 模拟的访问路径
        paths = [
            '/dashboard/',
            '/ranking/',
            '/analysis/',
            '/auth/login',
            '/auth/logout',
            '/dashboard/api/statistics',
            '/ranking/api/data',
            '/analysis/student/123456'
        ]
        
        # 模拟的IP地址
        ips = [
            '*************',
            '*************',
            '*************',
            '*********',
            '*********',
            '***********'
        ]
        
        # 模拟的User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        
        total_records = 0
        
        for i in range(7):
            # 计算日期
            date = today - timedelta(days=6-i)
            
            # 每天的访问量在50-200之间随机
            daily_access_count = random.randint(50, 200)
            
            for j in range(daily_access_count):
                # 随机生成访问时间（在当天内）
                hour = random.randint(8, 22)  # 主要在工作时间
                minute = random.randint(0, 59)
                second = random.randint(0, 59)
                
                access_time = date.replace(hour=hour, minute=minute, second=second)
                
                # 随机选择访问参数
                user_id = random.choice(user_ids)
                ip_address = random.choice(ips)
                user_agent = random.choice(user_agents)
                request_path = random.choice(paths)
                request_method = 'GET' if random.random() > 0.1 else 'POST'
                response_status = 200 if random.random() > 0.05 else random.choice([404, 500])
                response_time = random.uniform(50, 500)  # 50-500ms
                
                # 插入访问记录
                cursor.execute('''
                    INSERT INTO access_logs 
                    (user_id, ip_address, user_agent, request_path, request_method, 
                     response_status, response_time, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (user_id, ip_address, user_agent, request_path, request_method,
                      response_status, response_time, access_time.strftime('%Y-%m-%d %H:%M:%S')))
                
                total_records += 1
        
        conn.commit()
        print(f"成功生成 {total_records} 条测试访问数据")
        
        # 显示统计信息
        cursor.execute('''
            SELECT 
                DATE(created_at) as access_date,
                COUNT(*) as access_count
            FROM access_logs 
            GROUP BY DATE(created_at)
            ORDER BY access_date
        ''')
        
        daily_stats = cursor.fetchall()
        print("\n每日访问统计:")
        for stat in daily_stats:
            print(f"  {stat[0]}: {stat[1]} 次访问")
            
    except Exception as e:
        conn.rollback()
        print(f"生成测试数据失败: {e}")
        raise e
    finally:
        conn.close()

def generate_test_system_logs():
    """生成测试系统日志数据"""
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 生成一些系统日志用于测试分页功能
        actions = [
            '用户登录',
            '用户登出',
            '查看学生详情',
            '导出数据',
            '修改密码',
            '查看排名',
            '班级分析',
            '系统配置'
        ]
        
        descriptions = [
            '用户成功登录系统',
            '用户退出登录',
            '查看学生详细信息',
            '导出Excel数据文件',
            '修改登录密码',
            '查看学生排名信息',
            '进行班级数据分析',
            '修改系统配置参数'
        ]
        
        # 生成50条测试日志
        for i in range(50):
            action_idx = random.randint(0, len(actions) - 1)
            user_id = random.randint(1, 5)
            
            # 随机生成时间（近30天内）
            days_ago = random.randint(0, 30)
            log_time = datetime.now() - timedelta(days=days_ago)
            
            cursor.execute('''
                INSERT INTO system_logs (user_id, action, description, created_at)
                VALUES (?, ?, ?, ?)
            ''', (user_id, actions[action_idx], descriptions[action_idx], 
                  log_time.strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        print("成功生成 50 条测试系统日志")
        
    except Exception as e:
        conn.rollback()
        print(f"生成测试系统日志失败: {e}")
        raise e
    finally:
        conn.close()

if __name__ == '__main__':
    print("开始生成测试数据...")
    generate_test_access_data()
    generate_test_system_logs()
    print("测试数据生成完成！")
