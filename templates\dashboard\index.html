{% extends "base.html" %}

{% block title %}主面板 - 学生学业数据管理与分析平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            数据概览
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.students }}</h4>
                        <p class="card-text">学生总数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.scores }}</h4>
                        <p class="card-text">成绩记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.semesters }}</h4>
                        <p class="card-text">学期数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.majors }}</h4>
                        <p class="card-text">专业数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-graduation-cap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    专业分布
                </h5>
            </div>
            <div class="card-body">
                <div id="majorChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    年级分布
                </h5>
            </div>
            <div class="card-body">
                <div id="gradeChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 服务器访问统计 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    服务器访问统计（近7天）
                </h5>
            </div>
            <div class="card-body">
                <div id="accessChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
{% if current_user.has_admin_permission() %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    最近活动
                </h5>
                <div class="d-flex align-items-center">
                    <span id="activityCount" class="text-muted me-3"></span>
                    <div class="input-group" style="width: 200px;">
                        <input type="number" id="pageInput" class="form-control form-control-sm" placeholder="页码"
                            min="1">
                        <button class="btn btn-outline-primary btn-sm" type="button" onclick="goToPage()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="recentActivities">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>

                <!-- 分页导航 -->
                <nav aria-label="活动分页" class="mt-3">
                    <ul class="pagination justify-content-center" id="activityPagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;

    $(document).ready(function () {
        // 加载统计数据并绘制图表
        loadStatistics();

        // 加载访问统计图表
        loadAccessStats();

        {% if current_user.has_admin_permission() %}
        // 加载最近活动
        loadRecentActivities(1);
        {% endif %}
    });

    function loadStatistics() {
        $.get('/dashboard/api/statistics')
            .done(function (response) {
                if (response.success) {
                    drawMajorChart(response.data.major_distribution);
                    drawGradeChart(response.data.grade_distribution);
                }
            })
            .fail(function () {
                console.error('加载统计数据失败');
            });
    }

    function loadAccessStats() {
        $.get('/dashboard/api/access_stats?days=7')
            .done(function (response) {
                if (response.success) {
                    drawAccessChart(response.data);
                }
            })
            .fail(function () {
                console.error('加载访问统计失败');
            });
    }

    function drawMajorChart(data) {
        const chart = echarts.init(document.getElementById('majorChart'));
        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            series: [{
                name: '专业分布',
                type: 'pie',
                radius: '70%',
                data: data.map(item => ({
                    name: item.major_name,
                    value: item.count
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
        chart.setOption(option);
    }

    function drawGradeChart(data) {
        const chart = echarts.init(document.getElementById('gradeChart'));
        const option = {
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: data.map(item => item.grade + '级')
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '学生数量',
                type: 'bar',
                data: data.map(item => item.count),
                itemStyle: {
                    color: '#4facfe'
                }
            }]
        };
        chart.setOption(option);
    }

    function drawAccessChart(data) {
        const chart = echarts.init(document.getElementById('accessChart'));

        // 准备数据，确保有7天的完整数据
        const today = new Date();
        const dates = [];
        const accessCounts = [];

        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            dates.push(date.getMonth() + 1 + '/' + date.getDate());

            // 查找对应日期的访问数据
            const dayData = data.daily_stats.find(item => item.access_date === dateStr);
            accessCounts.push(dayData ? dayData.access_count : 0);
        }

        const option = {
            title: {
                text: `总访问量: ${data.total_access}`,
                textStyle: {
                    fontSize: 14,
                    color: '#666'
                },
                left: 'right'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    return `${params[0].name}<br/>访问次数: ${params[0].value}`;
                }
            },
            xAxis: {
                type: 'category',
                data: dates,
                axisLabel: {
                    color: '#666'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#666'
                }
            },
            series: [{
                name: '访问次数',
                type: 'line',
                data: accessCounts,
                smooth: true,
                lineStyle: {
                    color: '#4facfe',
                    width: 3
                },
                itemStyle: {
                    color: '#4facfe'
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0, color: 'rgba(79, 172, 254, 0.3)'
                        }, {
                            offset: 1, color: 'rgba(79, 172, 254, 0.1)'
                        }]
                    }
                }
            }]
        };
        chart.setOption(option);
    }

    {% if current_user.has_admin_permission() %}
    function loadRecentActivities(page = 1) {
        currentPage = page;

        $.get(`/dashboard/api/recent_activities?page=${page}&per_page=20`)
            .done(function (response) {
                if (response.success) {
                    displayRecentActivities(response.data, response.pagination);
                }
            })
            .fail(function () {
                $('#recentActivities').html('<p class="text-muted">加载失败</p>');
            });
    }

    function displayRecentActivities(activities, pagination) {
        // 更新记录数显示
        $('#activityCount').text(`共 ${pagination.total} 条记录`);

        // 更新页码输入框的最大值
        $('#pageInput').attr('max', pagination.total_pages);

        // 显示活动列表
        let html = '';
        if (activities.length === 0) {
            html = '<p class="text-muted">暂无活动记录</p>';
        } else {
            html = '<div class="list-group">';
            activities.forEach(function (activity) {
                html += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${activity.action}</h6>
                        <small>${activity.created_at}</small>
                    </div>
                    <p class="mb-1">${activity.description || ''}</p>
                    <small>用户: ${activity.full_name || activity.username || '未知'}</small>
                </div>
            `;
            });
            html += '</div>';
        }
        $('#recentActivities').html(html);

        // 更新分页导航
        updatePagination(pagination);
    }

    function updatePagination(pagination) {
        currentPage = pagination.page;
        totalPages = pagination.total_pages;

        let paginationHtml = '';

        // 上一页按钮
        if (pagination.has_prev) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadRecentActivities(${pagination.page - 1}); return false;">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
            `;
        } else {
            paginationHtml += `
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-left"></i> 上一页</span>
                </li>
            `;
        }

        // 页码按钮
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.page + 2);

        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecentActivities(1); return false;">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            if (i === pagination.page) {
                paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecentActivities(${i}); return false;">${i}</a></li>`;
            }
        }

        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecentActivities(${pagination.total_pages}); return false;">${pagination.total_pages}</a></li>`;
        }

        // 下一页按钮
        if (pagination.has_next) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadRecentActivities(${pagination.page + 1}); return false;">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
        } else {
            paginationHtml += `
                <li class="page-item disabled">
                    <span class="page-link">下一页 <i class="fas fa-chevron-right"></i></span>
                </li>
            `;
        }

        $('#activityPagination').html(paginationHtml);
    }

    function goToPage() {
        const page = parseInt($('#pageInput').val());
        if (page && page >= 1 && page <= totalPages) {
            loadRecentActivities(page);
            $('#pageInput').val('');
        } else {
            alert(`请输入1到${totalPages}之间的页码`);
        }
    }

    // 页码输入框回车事件
    $(document).on('keypress', '#pageInput', function (e) {
        if (e.which === 13) {
            goToPage();
        }
    });
    {% endif %}
</script>
{% endblock %}