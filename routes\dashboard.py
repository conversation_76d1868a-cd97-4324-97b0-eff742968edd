#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主面板路由
显示数据概览、统计信息等
"""

from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from models.scholarship import ScholarshipData
from models.database import get_db_cursor, get_access_stats

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    """主面板首页"""
    scholarship_data = ScholarshipData()
    
    # 获取基础统计信息
    stats = scholarship_data.get_statistics()
    
    # 获取筛选条件
    filters = scholarship_data.get_available_filters()
    
    # 如果是学生用户，只显示自己的数据
    if current_user.is_student() and current_user.student_id:
        student_data = scholarship_data.get_student_detail(current_user.student_id)
        return render_template('dashboard/student_dashboard.html', 
                             student_data=student_data, 
                             stats=stats)
    
    # 管理员和教师显示完整的统计信息
    return render_template('dashboard/index.html', 
                         stats=stats, 
                         filters=filters)

@dashboard_bp.route('/api/statistics')
@login_required
def api_statistics():
    """获取统计数据API"""
    scholarship_data = ScholarshipData()
    
    try:
        # 基础统计
        stats = scholarship_data.get_statistics()
        
        # 年级分布统计
        with get_db_cursor() as cursor:
            cursor.execute('''
                SELECT grade, COUNT(DISTINCT student_id) as count
                FROM scores 
                GROUP BY grade 
                ORDER BY grade
            ''')
            grade_distribution = [dict(row) for row in cursor.fetchall()]
        
        # 专业分布统计
        with get_db_cursor() as cursor:
            cursor.execute('''
                SELECT m.major_name, COUNT(DISTINCT sc.student_id) as count
                FROM scores sc
                JOIN majors m ON sc.major_id = m.id
                GROUP BY m.major_name
                ORDER BY count DESC
            ''')
            major_distribution = [dict(row) for row in cursor.fetchall()]
        
        # 学期数据统计
        with get_db_cursor() as cursor:
            cursor.execute('''
                SELECT 
                    sem.academic_year, 
                    sem.semester,
                    COUNT(DISTINCT sc.student_id) as student_count,
                    AVG(sc.total_score) as avg_score
                FROM scores sc
                JOIN semesters sem ON sc.semester_id = sem.id
                GROUP BY sem.academic_year, sem.semester
                ORDER BY sem.academic_year DESC, sem.semester DESC
            ''')
            semester_stats = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'data': {
                'basic_stats': stats,
                'grade_distribution': grade_distribution,
                'major_distribution': major_distribution,
                'semester_stats': semester_stats
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/access_stats')
@login_required
def api_access_stats():
    """获取访问统计数据"""
    days = request.args.get('days', 7, type=int)

    try:
        stats = get_access_stats(days=days)
        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/recent_activities')
@login_required
def api_recent_activities():
    """获取最近活动数据（支持分页）"""
    if not current_user.has_admin_permission():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    offset = (page - 1) * per_page

    try:
        with get_db_cursor() as cursor:
            # 获取总记录数
            cursor.execute('''
                SELECT COUNT(*) as total
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
            ''')
            total = cursor.fetchone()['total']

            # 获取分页数据
            cursor.execute('''
                SELECT
                    sl.action,
                    sl.description,
                    sl.created_at,
                    u.username,
                    u.full_name
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                ORDER BY sl.created_at DESC
                LIMIT ? OFFSET ?
            ''', (per_page, offset))
            activities = [dict(row) for row in cursor.fetchall()]

        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page

        return jsonify({
            'success': True,
            'data': activities,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/performance_trends')
@login_required
def api_performance_trends():
    """获取成绩趋势数据"""
    academic_year = request.args.get('academic_year')
    major = request.args.get('major')
    
    try:
        scholarship_data = ScholarshipData()
        
        with get_db_cursor() as cursor:
            query = '''
                SELECT 
                    sem.academic_year,
                    sem.semester,
                    AVG(sc.total_score) as avg_total_score,
                    AVG(sc.academic_score) as avg_academic_score,
                    AVG(sc.comprehensive_score) as avg_comprehensive_score,
                    COUNT(*) as student_count
                FROM scores sc
                JOIN semesters sem ON sc.semester_id = sem.id
                JOIN majors m ON sc.major_id = m.id
                WHERE 1=1
            '''
            
            params = []
            if academic_year:
                query += ' AND sem.academic_year = ?'
                params.append(academic_year)
            if major:
                query += ' AND m.major_name LIKE ?'
                params.append(f'%{major}%')
            
            query += '''
                GROUP BY sem.academic_year, sem.semester
                ORDER BY sem.academic_year, sem.semester
            '''
            
            cursor.execute(query, params)
            trends = [dict(row) for row in cursor.fetchall()]
        
        return jsonify({
            'success': True,
            'data': trends
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/top_performers')
@login_required
def api_top_performers():
    """获取优秀学生数据"""
    limit = request.args.get('limit', 10, type=int)
    academic_year = request.args.get('academic_year')
    semester = request.args.get('semester', type=int)
    
    try:
        scholarship_data = ScholarshipData()
        
        # 如果是学生用户，只返回自己的数据
        if current_user.is_student() and current_user.student_id:
            student_data = scholarship_data.get_student_detail(current_user.student_id)
            return jsonify({
                'success': True,
                'data': student_data
            })
        
        # 获取排名前列的学生
        top_students = scholarship_data.get_ranking_data(
            academic_year=academic_year,
            semester=semester,
            order_by='total_score',
            order_desc=True,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'data': top_students
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
