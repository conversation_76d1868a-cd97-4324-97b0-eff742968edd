<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 学生学业数据管理与分析平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .change-password-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .change-password-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .change-password-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            border: none;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .password-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #4facfe;
        }
        
        .password-requirements .requirement {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .password-requirements .requirement i {
            width: 16px;
            margin-right: 0.5rem;
        }
        
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: #dc3545; width: 25%; }
        .strength-medium { background: #ffc107; width: 50%; }
        .strength-good { background: #fd7e14; width: 75%; }
        .strength-strong { background: #28a745; width: 100%; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="change-password-card">
                    <div class="change-password-header">
                        <h2 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            修改密码
                        </h2>
                        <p class="mb-0 mt-2">更新您的账户密码</p>
                    </div>
                    
                    <div class="change-password-body">
                        <!-- Flash消息 -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <!-- 密码要求 -->
                        <div class="password-requirements">
                            <h6 class="mb-2">密码要求：</h6>
                            <div class="requirement">
                                <i class="fas fa-check-circle text-success"></i>
                                至少6个字符
                            </div>
                            <div class="requirement">
                                <i class="fas fa-info-circle text-info"></i>
                                建议包含大小写字母、数字和特殊字符
                            </div>
                        </div>
                        
                        <form method="POST" id="changePasswordForm">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>当前密码
                                </label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">
                                    <i class="fas fa-key me-1"></i>新密码
                                </label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="strengthFill"></div>
                                    </div>
                                    <small id="strengthText" class="text-muted">请输入新密码</small>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-check me-1"></i>确认新密码
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                <div id="passwordMatch" class="mt-1"></div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="fas fa-save me-1"></i>
                                    更新密码
                                </button>
                                <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    返回主页
                                </a>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                您的密码将被安全加密存储
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            const passwordMatch = document.getElementById('passwordMatch');
            const submitBtn = document.getElementById('submitBtn');
            
            // 密码强度检测
            function checkPasswordStrength(password) {
                let strength = 0;
                let feedback = [];
                
                if (password.length >= 6) strength += 1;
                if (password.length >= 8) strength += 1;
                if (/[a-z]/.test(password)) strength += 1;
                if (/[A-Z]/.test(password)) strength += 1;
                if (/[0-9]/.test(password)) strength += 1;
                if (/[^A-Za-z0-9]/.test(password)) strength += 1;
                
                const strengthLevels = [
                    { class: '', text: '请输入新密码' },
                    { class: 'strength-weak', text: '密码强度：弱' },
                    { class: 'strength-weak', text: '密码强度：弱' },
                    { class: 'strength-medium', text: '密码强度：中等' },
                    { class: 'strength-good', text: '密码强度：良好' },
                    { class: 'strength-strong', text: '密码强度：强' },
                    { class: 'strength-strong', text: '密码强度：很强' }
                ];
                
                return strengthLevels[Math.min(strength, 6)];
            }
            
            // 检查密码匹配
            function checkPasswordMatch() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (confirmPassword === '') {
                    passwordMatch.innerHTML = '';
                    return true;
                }
                
                if (newPassword === confirmPassword) {
                    passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>密码匹配</small>';
                    return true;
                } else {
                    passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>密码不匹配</small>';
                    return false;
                }
            }
            
            // 更新提交按钮状态
            function updateSubmitButton() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                const currentPassword = document.getElementById('current_password').value;
                
                const isValid = currentPassword.length > 0 && 
                               newPassword.length >= 6 && 
                               confirmPassword.length >= 6 && 
                               newPassword === confirmPassword;
                
                submitBtn.disabled = !isValid;
                submitBtn.classList.toggle('opacity-50', !isValid);
            }
            
            // 新密码输入事件
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = checkPasswordStrength(password);
                
                // 更新强度条
                strengthFill.className = 'strength-fill ' + strength.class;
                strengthText.textContent = strength.text;
                strengthText.className = strength.class ? 'text-' + strength.class.split('-')[1] : 'text-muted';
                
                // 检查密码匹配
                checkPasswordMatch();
                updateSubmitButton();
            });
            
            // 确认密码输入事件
            confirmPasswordInput.addEventListener('input', function() {
                checkPasswordMatch();
                updateSubmitButton();
            });
            
            // 当前密码输入事件
            document.getElementById('current_password').addEventListener('input', updateSubmitButton);
            
            // 初始化
            updateSubmitButton();
            
            // 表单提交验证
            document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (newPassword !== confirmPassword) {
                    e.preventDefault();
                    alert('两次输入的新密码不一致，请重新确认！');
                    confirmPasswordInput.focus();
                    return false;
                }
                
                if (newPassword.length < 6) {
                    e.preventDefault();
                    alert('新密码长度至少6位，请重新输入！');
                    newPasswordInput.focus();
                    return false;
                }
                
                // 显示加载状态
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>正在更新...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html> 