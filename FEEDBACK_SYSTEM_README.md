# 用户反馈管理系统使用说明

## 系统概述

用户反馈管理系统是学生奖学金管理平台的重要组成部分，为用户提供了完整的反馈提交、查看和管理功能。系统支持三种反馈类型，具备完善的权限控制和状态管理机制。

## 功能特性

### 用户端功能

#### 1. 反馈提交
- **访问路径**: 导航栏 → 反馈 → 提交新反馈
- **支持的反馈类型**:
  - 错误报告：系统bug、功能异常、页面错误等
  - 功能建议：新功能需求、改进建议、用户体验优化等
  - 其他：使用咨询、意见建议、内容问题等
- **功能特点**:
  - 表单验证和字符计数
  - 支持截图上传（PNG、JPG、JPEG、GIF、BMP、WebP格式，最大16MB）
  - 实时字符计数提示
  - 反馈提交指南

#### 2. 我的反馈
- **访问路径**: 导航栏 → 反馈（学生用户自动跳转）
- **功能特点**:
  - 查看个人所有反馈记录
  - 按状态和类型筛选
  - 分页显示（每页20条记录）
  - 反馈状态实时更新显示
  - 支持查看反馈详情

### 管理员端功能

#### 1. 反馈管理
- **访问路径**: 导航栏 → 反馈（管理员/教师自动跳转）
- **统计信息**:
  - 总反馈数
  - 待处理反馈数
  - 处理中反馈数
  - 近7天新增反馈数
- **管理功能**:
  - 查看所有用户反馈
  - 按状态、类型筛选
  - 快速状态更新
  - 反馈删除（仅管理员）

#### 2. 反馈详情处理
- **功能特点**:
  - 完整的反馈信息展示
  - 用户基本信息显示
  - 状态更新和处理备注
  - 附件下载和预览
  - 操作历史时间线

## 反馈状态说明

| 状态 | 说明 | 颜色标识 |
|------|------|----------|
| 待处理 | 新提交的反馈，等待处理 | 黄色 |
| 已查看 | 管理员已查看，但未开始处理 | 蓝色 |
| 处理中 | 正在处理中 | 蓝色 |
| 已解决 | 问题已解决或建议已采纳 | 绿色 |
| 已关闭 | 反馈已关闭，不再处理 | 灰色 |

## 权限控制

### 学生用户
- 可以提交新反馈
- 只能查看自己提交的反馈
- 可以查看反馈处理状态和管理员备注
- 无法修改或删除已提交的反馈

### 教师用户
- 具备学生用户的所有权限
- 可以查看所有用户的反馈
- 可以更新反馈状态和添加处理备注
- 无法删除反馈

### 管理员用户
- 具备教师用户的所有权限
- 可以删除不当或重复的反馈
- 可以查看完整的反馈统计信息

## 文件上传说明

### 支持的文件格式
- PNG、JPG、JPEG、GIF、BMP、WebP

### 文件大小限制
- 最大文件大小：16MB

### 存储位置
- 文件存储在 `uploads/feedback/` 目录下
- 文件名格式：`{用户ID}_{时间戳}_{原文件名}`

### 安全措施
- 文件扩展名验证
- 文件大小限制
- 安全的文件名处理
- 权限控制的文件访问

## 数据库结构

### feedback表字段说明
- `id`: 主键，自增
- `user_id`: 用户ID，外键关联users表
- `type`: 反馈类型（bug_report/feature_request/other）
- `title`: 反馈标题
- `content`: 反馈内容
- `status`: 反馈状态（pending/viewed/in_progress/resolved/closed）
- `admin_notes`: 管理员处理备注
- `attachment_path`: 附件路径
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 索引优化
- `user_id`: 用户查询优化
- `status`: 状态筛选优化
- `type`: 类型筛选优化
- `created_at`: 时间排序优化

## 使用建议

### 对于用户
1. **明确反馈类型**：根据问题性质选择正确的反馈类型
2. **详细描述问题**：提供具体的问题描述和重现步骤
3. **上传截图**：对于界面问题，建议上传截图说明
4. **关注反馈状态**：定期查看反馈处理进度

### 对于管理员
1. **及时处理反馈**：尽快查看和处理新提交的反馈
2. **详细记录备注**：在处理备注中记录处理过程和解决方案
3. **合理使用状态**：根据处理进度及时更新反馈状态
4. **定期清理**：删除重复或不当的反馈记录

## 技术特点

### 前端技术
- Bootstrap 5 响应式设计
- Font Awesome 图标库
- JavaScript 表单验证和交互
- 蓝白色主题设计

### 后端技术
- Flask 框架
- SQLite 数据库
- 文件上传处理
- 权限控制装饰器

### 安全特性
- 用户身份验证
- 权限控制
- 文件上传安全验证
- SQL注入防护
- XSS防护

## 维护说明

### 定期维护任务
1. 清理过期的附件文件
2. 备份反馈数据
3. 监控系统性能
4. 更新安全配置

### 故障排除
1. 检查数据库连接
2. 验证文件权限
3. 查看应用日志
4. 测试文件上传功能

## 更新日志

### v1.0.0 (2024-06-12)
- 初始版本发布
- 实现完整的反馈管理功能
- 支持三种反馈类型
- 实现权限控制和状态管理
- 支持文件上传和分页功能
