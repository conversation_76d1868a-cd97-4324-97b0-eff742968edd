# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again. 

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- Due to <PERSON>urs<PERSON>'s limit, when you use `git` and `gh` and need to submit a multiline commit message, first write the message in a file, and then use `git commit -F <filename>` or similar command to commit. And then remove the file. Include "[Cursor] " in the commit message and PR title.

## Cursor learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- The user is using Windows and cannot use "&&" to connect two commands.
- 搜索功能显示NaN问题：search_students方法需要返回完整的成绩字段（total_score, academic_score等），而不仅仅是基本信息
- AI功能权限控制：前端模板使用`{% if not current_user.is_student() %}`条件判断，学生用户看不到AI功能区域是正常的权限控制
- 年级筛选：需要在模型的get_ranking_data方法中添加grade参数支持，并在所有相关API路由中传递此参数
- 前端NaN显示问题：JavaScript中parseFloat()处理null/undefined时返回NaN，需要添加空值检查
- 浏览器缓存问题：前端模板修改后需要禁用缓存或强制刷新才能看到效果
- 列配置映射问题：前端列配置需要精确对应表格实际结构，避免索引错位导致显隐失效
- AI API端点配置问题：**关键发现** - 用户的test.py使用阿里云百炼代理API `https://dashscope.aliyuncs.com/compatible-mode/v1`，不是DeepSeek官方API。Flask应用必须与test.py保持完全一致的配置才能工作
- AI服务URL路径处理：需要在请求时自动添加`/chat/completions`路径，因为原始配置只包含base URL
- 多字段排序实现：使用JSON序列化在前后端传递复杂排序参数，提高排序功能灵活性
- 重置功能完整性：重置操作需要同时清理表单、配置存储和视觉状态，确保一致性
- AI超时配置：deepseek-r1模型响应较慢，需要设置长超时时间（300秒），并添加详细的错误处理和用户提示
- AI请求优化：确保不使用stream模式，使用标准的非流式请求以避免解析问题，同时优化prompt以获得更好的分析结果
- AI结果美化：添加markdown渲染支持，使用marked.js和highlight.js实现专业的分析报告展示效果
- marked.js使用方法：使用`marked.parse(markdownContent)`进行markdown到HTML的转换，需要确保库已正确加载
- highlight.js代码高亮：使用`hljs.highlightAll()`对所有代码块进行语法高亮，需要检查库的可用性
- JavaScript错误处理：在markdown渲染时添加try-catch错误处理，如果渲染失败则显示原始内容作为备用方案
- 专业显示格式问题：后端get_available_filters()返回majors为字典列表（含major_name字段），前端模板必须使用{{ major.major_name }}而不是{{ major }}，否则会显示整个字典对象字符串表示
- 班级分析状态残留问题：当筛选结果为空时，showError函数会用错误信息替换整个容器内容，导致后续有数据时页面结构被破坏。解决方案是：1）创建resetClassOverviewStructure()函数重置原始HTML结构，2）创建showClassOverviewError()函数在不破坏结构的情况下显示错误，3）在displayClassOverview()开始时调用结构重置
- 班级详细分析分页限制问题：班级详细分析使用数据库API获取学生数据，但该API默认有分页限制（per_page=20），导致班级概览显示32人但详情只显示7人的问题。解决方案：在调用`/ranking/api/data`时添加`per_page: '500'`参数，确保获取该班级所有学生数据
- 模板语法错误：在Jinja2模板中不能在JavaScript中使用Flask变量语法，需要避免在客户端JavaScript中使用服务器端模板语法

# Scratchpad

## 当前任务：用户反馈管理系统

### 任务要求：
实现完整的用户反馈管理系统，包含以下具体功能模块：

**用户端功能：**
1. 反馈提交页面：用户可以提交反馈信息，包括反馈类型（错误报告/功能建议/其他）、标题、详细描述、可选的截图上传
2. 我的反馈页面：用户可以查看自己提交的所有反馈记录，显示反馈状态（待处理/已查看/已解决/已关闭）、提交时间、处理进度

**管理员端功能：**
1. 反馈管理页面：管理员可以查看所有用户反馈，支持按状态、类型、时间范围筛选
2. 反馈详情处理：管理员可以查看反馈详情，标记状态（已查看/处理中/已解决），添加处理备注
3. 反馈删除功能：管理员可以删除不当或重复的反馈信息

**技术要求：**
- 使用Flask + SQLite架构，遵循项目现有的代码结构
- 创建feedback表存储反馈数据，包含字段：id、user_id、type、title、content、status、created_at、updated_at、admin_notes
- 实现权限控制：普通用户只能查看自己的反馈，管理员可以查看所有反馈
- 页面设计使用Bootstrap 5 + 蓝白主题，保持与现有页面风格一致
- 实现分页功能（每页20条记录）和状态统计

### 实施计划：
[X] 1. 数据库设计和创建
   - [X] 创建feedback表存储反馈数据
   - [X] 设计表结构：id、user_id、type、title、content、status、created_at、updated_at、admin_notes、attachment_path
   - [X] 添加必要的索引以提高查询性能
   - [X] 实现数据库迁移逻辑，兼容现有表结构

[X] 2. 后端路由和API开发
   - [X] 创建feedback蓝图和路由
   - [X] 实现反馈提交API
   - [X] 实现反馈列表API（支持分页和筛选）
   - [X] 实现反馈详情和状态更新API
   - [X] 实现反馈删除API（仅管理员）
   - [X] 集成到主应用中

[X] 3. 前端页面开发
   - [X] 创建反馈提交页面（用户端）
   - [X] 创建我的反馈页面（用户端）
   - [X] 创建反馈管理页面（管理员端）
   - [X] 创建反馈详情处理页面（管理员端）
   - [X] 在导航栏中添加反馈功能入口

[X] 4. 权限控制实现
   - [X] 实现用户只能查看自己反馈的权限控制
   - [X] 实现管理员可以查看所有反馈的权限控制
   - [X] 实现反馈状态更新和删除的权限控制
   - [X] 使用现有的装饰器系统

[X] 5. 文件上传功能
   - [X] 实现截图上传功能
   - [X] 配置文件存储路径和安全验证
   - [X] 实现文件下载和预览功能
   - [X] 创建uploads/feedback目录

[X] 6. 测试和验证
   - [X] 生成测试数据（30条反馈记录）
   - [X] 应用程序成功启动，无错误
   - [X] 数据库迁移成功，兼容现有结构
   - [X] 导航栏反馈入口正常显示
   - [X] 管理员可以访问反馈管理页面
   - [X] 页面模板渲染正常

### 功能完成状态：
✅ **用户反馈管理系统已完全实现**

#### 已实现的核心功能：

**用户端功能：**
1. ✅ **反馈提交页面** - 支持三种反馈类型（错误报告/功能建议/其他）
   - 表单验证和字符计数
   - 文件上传功能（支持多种图片格式）
   - 用户友好的提交指南

2. ✅ **我的反馈页面** - 用户可查看自己的所有反馈
   - 状态和类型筛选功能
   - 分页显示（每页20条）
   - 反馈状态实时显示

**管理员端功能：**
1. ✅ **反馈管理页面** - 管理员查看所有用户反馈
   - 统计卡片显示（总数、待处理、处理中、近7天新增）
   - 高级筛选功能（状态、类型、时间范围）
   - 批量状态更新功能
   - 反馈删除功能（仅管理员）

2. ✅ **反馈详情处理页面** - 详细的反馈处理界面
   - 完整的反馈信息展示
   - 状态更新和备注添加
   - 附件下载和预览
   - 操作历史时间线

**技术实现特点：**
- ✅ **数据库设计** - 完整的feedback表结构，包含所有必要字段
- ✅ **权限控制** - 学生只能查看自己的反馈，管理员可查看所有
- ✅ **文件上传** - 安全的文件上传和存储机制
- ✅ **分页功能** - 高效的分页查询和导航
- ✅ **响应式设计** - Bootstrap 5 + 蓝白主题，美观现代
- ✅ **数据库迁移** - 兼容现有表结构，平滑升级

**测试数据：**
- ✅ 生成30条测试反馈数据
- ✅ 涵盖所有反馈类型和状态
- ✅ 模拟真实使用场景

## 之前完成的任务记录

### ✅ 数据库优化和用户管理增强（已完成）

**最终成果：**
1. ✅ 删除显示列配置功能 - 完全移除，界面简化
2. ✅ 增强学生详情显示 - 完整分数信息展示
3. ✅ 批量创建学生账户 - 2701个学生账户批量创建

**验证状态：**
- 🔹 数据库完整性：✅ 通过
- 🔹 学生详情API：✅ 返回完整字段  
- 🔹 账户创建：✅ 批量处理完成
- 🔹 登录功能：✅ 密码验证成功

**账户信息：**
- 用户名：学号（如202012596）
- 密码：neuq2025
- 覆盖率：已完成所有学生

基于 prd.txt 技术文档，需要实现一个完整的学生学业数据管理与分析平台，包含：
- 后端：Python Flask
- 前端：HTML + CSS + JS
- 图表：ECharts
- 设计风格：蓝白色调现代化界面
- 现有基础：scholarship_analyzer.py（数据解析）+ scholarship_data.db（SQLite数据库）

### 主要功能模块
1. 数据库显示（支持排序、导出、AI辅助SQL查询）
2. 详情与分析（学生个人分析、智能分析报告）
3. 主面板（数据概览、统计信息）
4. API管理系统（deepseek-r1模型配置）
5. 数据库日志系统
6. 导入导出模块
7. 班级分析页面
8. 用户管理（管理员/教师/学生权限）

### 开发计划
[X] 1. 项目结构设计和初始化
[X] 2. Flask后端框架搭建
[X] 3. 用户认证和权限管理系统
[X] 4. 数据库扩展（用户表、日志表等）
[X] 5. 数据库功能实现（完整功能：多字段排序、筛选、导出、AI辅助SQL查询）
[X] 6. 学生详情和分析功能
[X] 7. 主面板和统计功能
[X] 8. AI集成（deepseek-r1）- AI服务框架已集成，API已修复可正常使用
[X] 9. 班级分析功能 - 已完成
[X] 10. 用户密码管理功能 - 已完成
[ ] 11. 导入导出功能
[ ] 12. 前端界面开发
[ ] 13. 测试和优化