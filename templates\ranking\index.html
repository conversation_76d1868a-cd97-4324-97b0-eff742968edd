{% extends "base.html" %}

{% block title %}数据库 - 学生学业数据管理与分析平台{% endblock %}

{% block extra_head %}
<!-- 禁用缓存 -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-trophy me-2"></i>
            数据库查询
        </h1>
    </div>
</div>

<div class="row">
    <!-- 数据库主要内容 -->
    <div class="col-12">
        <!-- 筛选条件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    筛选条件
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="academicYear" class="form-label">学年</label>
                        <select class="form-select" id="academicYear" name="academic_year">
                            <option value="">全部学年</option>
                            {% for semester in filters.semesters %}
                            <option value="{{ semester.academic_year }}">{{ semester.academic_year }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="semester" class="form-label">学期</label>
                        <select class="form-select" id="semester" name="semester">
                            <option value="">全部学期</option>
                            <option value="1">第一学期</option>
                            <option value="2">第二学期</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="major" class="form-label">专业</label>
                        <select class="form-select" id="major" name="major">
                            <option value="">全部专业</option>
                            {% for major in filters.majors %}
                            <option value="{{ major.major_name }}">{{ major.major_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="grade" class="form-label">年级</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">全部年级</option>
                            {% for grade in filters.grades %}
                            <option value="{{ grade }}">{{ grade }}级</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary me-2" id="resetBtn">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-success me-2" id="exportBtn">
                            <i class="fas fa-download me-1"></i>导出数据
                        </button>
                    </div>
                </form>
            </div>
        </div>



        <!-- 搜索框 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索学号或姓名...">
                    <button class="btn btn-primary" type="button" id="searchBtn">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                        <i class="fas fa-times me-1"></i>清除
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div id="resultInfo" class="text-muted"></div>
            </div>
        </div>

        <!-- 数据库表格 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            数据库查询结果
                        </h5>
                        {% if not current_user.is_student() %}
                        <button type="button" class="btn btn-outline-primary btn-sm" id="aiAssistantBtn">
                            <i class="fas fa-robot me-1"></i>AI助手
                        </button>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载数据...</p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover ranking-table" id="rankingTable">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>学号</th>
                                        <th>姓名</th>
                                        <th>专业</th>
                                        <th>班级</th>
                                        <th>学年学期</th>
                                        <th>总分</th>
                                        <th>学业成绩</th>
                                        <th>综合素质分</th>
                                        <th>奖学金等级</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="rankingTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="数据库分页" id="paginationContainer" style="display: none;">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- AI助手模态框 -->
<div class="modal fade" id="aiAssistantModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-robot me-2"></i>
                    AI辅助SQL查询
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- AI对话区域 -->
                <div class="mb-3">
                    <label for="aiQuery" class="form-label">向AI描述您的查询需求：</label>
                    <textarea class="form-control" id="aiQuery" rows="3"
                        placeholder="例如：查询2023-2024学年第一学期计算机专业总分前10名的学生"></textarea>
                </div>

                <div class="mb-3">
                    <button type="button" class="btn btn-primary btn-sm me-2" id="generateSqlBtn">
                        <i class="fas fa-magic me-1"></i>生成SQL
                    </button>
                    <button type="button" class="btn btn-success btn-sm" id="executeSqlBtn" disabled>
                        <i class="fas fa-play me-1"></i>执行查询
                    </button>
                </div>

                <!-- 生成的SQL显示区域 -->
                <div class="mb-3" id="sqlResultArea" style="display: none;">
                    <label class="form-label">生成的SQL语句：</label>
                    <div class="position-relative">
                        <textarea class="form-control" id="generatedSql" rows="4" readonly></textarea>
                        <button type="button" class="btn btn-outline-secondary btn-sm position-absolute top-0 end-0 m-1"
                            id="copySqlBtn" title="复制SQL">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>

                <!-- AI分析结果 -->
                <div id="aiAnalysisArea" style="display: none;">
                    <label class="form-label">AI分析结果：</label>
                    <div class="ai-analysis" id="aiAnalysisContent">
                        <!-- AI分析内容 -->
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="aiLoadingArea" class="text-center" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">AI处理中...</span>
                    </div>
                    <small class="text-muted ms-2">AI正在处理您的请求...</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-outline-danger" onclick="clearAIData()">
                    <i class="fas fa-trash me-1"></i>清空
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 学生详情模态框 -->
<div class="modal fade" id="studentDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    学生详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="viewStudentAnalysis()">查看分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    $(document).ready(function () {
        let currentPage = 1;
        let currentFilters = {};

        // 初始加载数据
        loadRankingData();
        
        // 筛选表单提交
        $('#filterForm').on('submit', function (e) {
            e.preventDefault();
            currentPage = 1;
            loadRankingData();
        });

        // 重置按钮
        $('#resetBtn').on('click', function () {
            $('#filterForm')[0].reset();
            
            currentPage = 1;
            loadRankingData();
        });

        // 搜索功能
        $('#searchBtn').on('click', function () {
            performSearch();
        });

        $('#clearSearchBtn').on('click', function () {
            $('#searchInput').val('');
            loadRankingData();
        });

        // 支持回车键搜索
        $('#searchInput').on('keypress', function (e) {
            if (e.which === 13) {  // Enter key
                performSearch();
            }
        });

        function performSearch() {
            const keyword = $('#searchInput').val().trim();
            if (keyword.length >= 1) {
                searchStudents(keyword);
            } else {
                loadRankingData();
            }
        }

        // 导出按钮
        $('#exportBtn').on('click', function () {
            exportData();
        });

        // AI助手按钮
        $('#aiAssistantBtn').on('click', function () {
            $('#aiAssistantModal').modal('show');
        });

        // AI功能按钮
        $('#generateSqlBtn').on('click', function () {
            generateAISQL();
        });

        $('#executeSqlBtn').on('click', function () {
            executeGeneratedSQL();
        });

        $('#copySqlBtn').on('click', function () {
            copyToClipboard($('#generatedSql').val());
        });
    });

    function loadRankingData(page = 1) {
        currentPage = page;
        showLoading(true);

        // 获取筛选条件
        const formData = new FormData($('#filterForm')[0]);
        const params = new URLSearchParams();

        for (let [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        params.append('page', page);
        params.append('per_page', 20);

        currentFilters = Object.fromEntries(params);

        $.get('/ranking/api/data?' + params.toString())
            .done(function (response) {
                if (response.success) {
                    displayRankingData(response.data);
                    updatePagination(response.page, response.per_page, response.total);
                    updateResultInfo(response.total, response.page, response.per_page);
                } else {
                    showError('加载数据失败: ' + response.error);
                }
            })
            .fail(function () {
                showError('网络错误，请稍后重试');
            })
            .always(function () {
                showLoading(false);
            });
    }

    function displayRankingData(data) {
        const tbody = $('#rankingTableBody');
        tbody.empty();

        if (data.length === 0) {
            tbody.append(`
            <tr>
                <td colspan="11" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无数据
                </td>
            </tr>
        `);
            return;
        }

        data.forEach((student, index) => {
            const rank = student.total_rank || ((currentPage - 1) * 20 + index + 1);
            const rankClass = getRankClass(rank);

            const row = `
            <tr class="${rankClass}">
                <td>
                    <span class="badge ${getRankBadgeClass(rank)}">${rank}</span>
                </td>
                <td>${student.student_id}</td>
                <td>${student.name}</td>
                <td>${student.major_name}</td>
                <td>${student.class_name}</td>
                <td>${student.academic_year}-${student.semester}</td>
                <td>
                    <span class="badge bg-primary">${student.total_score && !isNaN(student.total_score) ? parseFloat(student.total_score).toFixed(2) : '未知'}</span>
                </td>
                <td>${student.academic_score && !isNaN(student.academic_score) ? parseFloat(student.academic_score).toFixed(2) : '未知'}</td>
                <td>${student.comprehensive_score && !isNaN(student.comprehensive_score) ? parseFloat(student.comprehensive_score).toFixed(2) : '未知'}</td>
                <td>
                    ${student.award_level ?
                    `<span class="badge ${getAwardBadgeClass(student.award_level)}">${student.award_level}</span>` :
                    '<span class="text-muted">无</span>'
                }
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="showStudentDetail('${student.student_id}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="viewStudentAnalysis('${student.student_id}')">
                        <i class="fas fa-chart-line"></i> 分析
                    </button>
                </td>
            </tr>
        `;
            tbody.append(row);
        });
    }

    function getRankClass(rank) {
        if (rank <= 3) return 'rank-top';
        if (rank <= 10) return 'rank-good';
        return '';
    }

    function getRankBadgeClass(rank) {
        if (rank === 1) return 'bg-warning';
        if (rank === 2) return 'bg-secondary';
        if (rank === 3) return 'bg-info';
        if (rank <= 10) return 'bg-success';
        return 'bg-light text-dark';
    }

    function getAwardBadgeClass(award) {
        if (award.includes('一等')) return 'bg-warning';
        if (award.includes('二等')) return 'bg-info';
        if (award.includes('三等')) return 'bg-success';
        return 'bg-secondary';
    }

    function updatePagination(page, perPage, total) {
        const totalPages = Math.ceil(total / perPage);
        const pagination = $('#pagination');
        pagination.empty();

        if (totalPages <= 1) {
            $('#paginationContainer').hide();
            return;
        }

        $('#paginationContainer').show();

        // 上一页
        pagination.append(`
        <li class="page-item ${page === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadRankingData(${page - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `);

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            pagination.append(`
            <li class="page-item ${i === page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadRankingData(${i}); return false;">${i}</a>
            </li>
        `);
        }

        // 下一页
        pagination.append(`
        <li class="page-item ${page === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadRankingData(${page + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `);
    }

    function updateResultInfo(total, page, perPage) {
        const start = (page - 1) * perPage + 1;
        const end = Math.min(page * perPage, total);
        $('#resultInfo').text(`显示第 ${start}-${end} 条，共 ${total} 条记录`);
    }

    function searchStudents(keyword) {
        showLoading(true);

        const params = new URLSearchParams();
        params.append('keyword', keyword);

        // 添加当前筛选条件
        const formData = new FormData($('#filterForm')[0]);
        for (let [key, value] of formData.entries()) {
            if (value && key !== 'order_by') {
                params.append(key, value);
            }
        }

        $.get('/ranking/api/search?' + params.toString())
            .done(function (response) {
                if (response.success) {
                    displayRankingData(response.data);
                    $('#paginationContainer').hide();
                    updateResultInfo(response.data.length, 1, response.data.length);
                } else {
                    showError('搜索失败: ' + response.error);
                }
            })
            .fail(function () {
                showError('搜索失败，请稍后重试');
            })
            .always(function () {
                showLoading(false);
            });
    }

    function showStudentDetail(studentId) {
        // 存储学生ID到模态框数据中
        $('#studentDetailModal').data('student-id', studentId);
        
        // 显示加载状态
        $('#studentDetailContent').html(`
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载学生详情...</p>
        </div>
    `);

        $('#studentDetailModal').modal('show');

        // 加载学生完整详情信息
        $.ajax({
            url: `/analysis/api/student/${studentId}/trends`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    const latestRecord = response.data[response.data.length - 1];
                    
                    // 安全的数值格式化函数
                    const formatScore = (score) => score && !isNaN(score) ? parseFloat(score).toFixed(2) : '未知';
                    const formatRank = (rank) => rank && !isNaN(rank) ? rank : 'N/A';
                    
                    const detailHtml = `
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-user me-2"></i>基本信息</h6>
                                <table class="table table-sm table-striped">
                                    <tr><td><strong>学号：</strong></td><td>${studentId}</td></tr>
                                    <tr><td><strong>最新学期：</strong></td><td>${latestRecord.semester}</td></tr>
                                    <tr><td><strong>总分：</strong></td><td><span class="badge bg-primary">${formatScore(latestRecord.total_score)}</span></td></tr>
                                    <tr><td><strong>总排名：</strong></td><td><span class="badge bg-success">${formatRank(latestRecord.total_rank)}</span></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-graduation-cap me-2"></i>学业成绩</h6>
                                <table class="table table-sm table-striped">
                                    <tr><td><strong>学业成绩：</strong></td><td>${formatScore(latestRecord.academic_score)}</td></tr>
                                    <tr><td><strong>学业排名：</strong></td><td>${formatRank(latestRecord.academic_rank)}</td></tr>
                                    <tr><td><strong>学业百分比：</strong></td><td>${formatScore(latestRecord.academic_percentage * 100)}%</td></tr>
                                    <tr><td><strong>学业加权分：</strong></td><td>${formatScore(latestRecord.academic_weighted)}</td></tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-heart me-2"></i>综合素质分</h6>
                                <table class="table table-sm table-striped">
                                    <tr><td><strong>综合素质总分：</strong></td><td><span class="badge bg-info">${formatScore(latestRecord.comprehensive_score)}</span></td></tr>
                                    <tr><td><strong>综合素质加权：</strong></td><td>${formatScore(latestRecord.comprehensive_weighted)}</td></tr>
                                    <tr><td><strong>思想品德分：</strong></td><td>${formatScore(latestRecord.moral_score)}</td></tr>
                                    <tr><td><strong>社会工作分：</strong></td><td>${formatScore(latestRecord.social_work_score)}</td></tr>
                                    <tr><td><strong>科研创新分：</strong></td><td>${formatScore(latestRecord.research_score)}</td></tr>
                                    <tr><td><strong>集体建设分：</strong></td><td>${formatScore(latestRecord.collective_score)}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-running me-2"></i>活动分数详情</h6>
                                <table class="table table-sm table-striped">
                                    <tr><td><strong>活动总分：</strong></td><td><span class="badge bg-warning">${formatScore(latestRecord.activity_total_score)}</span></td></tr>
                                    <tr><td><strong>活动基础分：</strong></td><td>${formatScore(latestRecord.activity_base_score)}</td></tr>
                                    <tr><td><strong>活动体育分：</strong></td><td>${formatScore(latestRecord.activity_exercise_score)}</td></tr>
                                    <tr><td><strong>活动赛事分：</strong></td><td>${formatScore(latestRecord.activity_event_score)}</td></tr>
                                </table>
                                
                                <h6 class="mt-3"><i class="fas fa-award me-2"></i>奖励信息</h6>
                                <table class="table table-sm table-striped">
                                    <tr><td><strong>奖学金等级：</strong></td><td>${latestRecord.award_level || '<span class="text-muted">无</span>'}</td></tr>
                                    <tr><td><strong>备注：</strong></td><td>${latestRecord.remarks || '<span class="text-muted">无</span>'}</td></tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><i class="fas fa-chart-line me-2"></i>历史学期记录</h6>
                                <p class="text-muted">该学生共有 ${response.data.length} 个学期的成绩记录</p>
                                <div class="d-flex gap-2 flex-wrap">
                                    ${response.data.map(record => `
                                        <span class="badge bg-secondary">${record.semester} (总分: ${formatScore(record.total_score)})</span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                    $('#studentDetailContent').html(detailHtml);
                } else {
                    $('#studentDetailContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            未找到该学生的数据记录
                        </div>
                    `);
                }
            },
            error: function() {
                $('#studentDetailContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载学生详情失败，请稍后重试
                    </div>
                `);
            }
        });
    }

    function exportData() {
        const params = new URLSearchParams(currentFilters);
        window.open('/ranking/export?' + params.toString(), '_blank');
    }

    function showLoading(show) {
        if (show) {
            $('#loadingSpinner').show();
            $('#rankingTable').hide();
        } else {
            $('#loadingSpinner').hide();
            $('#rankingTable').show();
        }
    }

    function showError(message) {
        $('#rankingTableBody').html(`
        <tr>
            <td colspan="11" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                ${message}
            </td>
        </tr>
    `);
    }

    // AI功能相关函数
    function generateAISQL() {
        const query = $('#aiQuery').val().trim();
        if (!query) {
            alert('请输入查询需求描述');
            return;
        }

        $('#aiLoadingArea').show();
        $('#generateSqlBtn').prop('disabled', true);

        $.ajax({
            url: '/api/sql_assistant',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                description: query
            }),
            success: function (response) {
                if (response.success) {
                    $('#generatedSql').val(response.data.sql);
                    $('#sqlResultArea').show();
                    $('#executeSqlBtn').prop('disabled', false);

                    // 显示AI分析说明
                    $('#aiAnalysisContent').html(`
                        <div class="alert alert-info">
                            <strong>查询描述：</strong>${response.data.description}<br>
                            <strong>生成的SQL：</strong>已显示在上方文本框中
                        </div>
                    `);
                    $('#aiAnalysisArea').show();
                } else {
                    alert('生成SQL失败: ' + response.error);
                }
            },
            error: function () {
                alert('生成SQL失败，请稍后重试');
            },
            complete: function () {
                $('#aiLoadingArea').hide();
                $('#generateSqlBtn').prop('disabled', false);
            }
        });
    }

    function executeGeneratedSQL() {
        const sql = $('#generatedSql').val();
        if (!sql) {
            alert('没有可执行的SQL语句');
            return;
        }

        $('#aiLoadingArea').show();
        $('#executeSqlBtn').prop('disabled', true);

        $.ajax({
            url: '/api/execute_sql',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                sql: sql
            }),
            success: function (response) {
                if (response.success) {
                    // 显示查询结果
                    displayRankingData(response.data.results);
                    $('#paginationContainer').hide();
                    updateResultInfo(response.data.count, 1, response.data.count);
                } else {
                    alert('执行SQL失败: ' + response.error);
                }
            },
            error: function () {
                alert('执行SQL失败，请稍后重试');
            },
            complete: function () {
                $('#aiLoadingArea').hide();
                $('#executeSqlBtn').prop('disabled', false);
            }
        });
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function () {
            // 显示复制成功提示
            const btn = $('#copySqlBtn');
            const originalHtml = btn.html();
            btn.html('<i class="fas fa-check"></i>');
            setTimeout(() => {
                btn.html(originalHtml);
            }, 1000);
        }).catch(function () {
            alert('复制失败，请手动复制');
        });
    }





    function viewStudentAnalysis(studentId) {
        // 跳转到学生分析页面
        if (!studentId) {
            // 如果没有传入studentId，尝试从模态框获取
            studentId = $('#studentDetailModal').data('student-id');
        }
        
        if (studentId) {
            window.open(`/analysis/student/${studentId}`, '_blank');
        } else {
            alert('无法获取学生ID');
        }
    }

    // 清空AI数据
    function clearAIData() {
        $('#aiQuery').val('');
        $('#generatedSql').val('');
        $('#sqlResultArea').hide();
        $('#aiAnalysisArea').hide();
        $('#aiAnalysisContent').empty();
        $('#executeSqlBtn').prop('disabled', true);
    }
</script>
{% endblock %}