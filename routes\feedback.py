#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反馈管理路由
处理用户反馈提交、查看、管理等功能
"""

import os
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models.database import (
    create_feedback, get_feedback_list, get_feedback_by_id,
    update_feedback_status, delete_feedback, get_feedback_statistics
)
from utils.decorators import admin_required, teacher_or_admin_required

feedback_bp = Blueprint('feedback', __name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@feedback_bp.route('/')
@login_required
def index():
    """反馈首页 - 根据用户角色显示不同内容"""
    if current_user.is_admin() or current_user.is_teacher():
        # 管理员和教师显示反馈管理页面
        return redirect(url_for('feedback.admin_list'))
    else:
        # 学生显示我的反馈页面
        return redirect(url_for('feedback.my_feedback'))

@feedback_bp.route('/submit', methods=['GET', 'POST'])
@login_required
def submit():
    """提交反馈"""
    if request.method == 'GET':
        return render_template('feedback/submit.html')
    
    try:
        # 获取表单数据
        feedback_type = request.form.get('type', 'other')
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        
        # 验证必填字段
        if not title or not content:
            flash('标题和内容不能为空', 'error')
            return render_template('feedback/submit.html')
        
        # 处理文件上传
        attachment_path = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # 添加用户ID和时间戳避免文件名冲突
                import time
                filename = f"{current_user.id}_{int(time.time())}_{filename}"
                
                # 确保上传目录存在
                upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'feedback')
                os.makedirs(upload_dir, exist_ok=True)
                
                file_path = os.path.join(upload_dir, filename)
                file.save(file_path)
                attachment_path = f"feedback/{filename}"
        
        # 创建反馈
        feedback_id = create_feedback(
            user_id=current_user.id,
            feedback_type=feedback_type,
            title=title,
            content=content,
            attachment_path=attachment_path
        )
        
        flash('反馈提交成功，我们会尽快处理', 'success')
        return redirect(url_for('feedback.my_feedback'))
        
    except Exception as e:
        flash(f'提交反馈失败: {str(e)}', 'error')
        return render_template('feedback/submit.html')

@feedback_bp.route('/my')
@login_required
def my_feedback():
    """我的反馈"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    feedback_type = request.args.get('type', '')
    
    # 获取当前用户的反馈列表
    result = get_feedback_list(
        user_id=current_user.id,
        status=status if status else None,
        feedback_type=feedback_type if feedback_type else None,
        page=page,
        per_page=20
    )
    
    return render_template('feedback/my_feedback.html', 
                         feedback_list=result['feedback_list'],
                         pagination=result['pagination'],
                         current_status=status,
                         current_type=feedback_type)

@feedback_bp.route('/admin')
@teacher_or_admin_required
def admin_list():
    """管理员反馈管理页面"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    feedback_type = request.args.get('type', '')
    
    # 获取所有反馈列表
    result = get_feedback_list(
        status=status if status else None,
        feedback_type=feedback_type if feedback_type else None,
        page=page,
        per_page=20
    )
    
    # 获取统计信息
    stats = get_feedback_statistics()
    
    return render_template('feedback/admin_list.html',
                         feedback_list=result['feedback_list'],
                         pagination=result['pagination'],
                         stats=stats,
                         current_status=status,
                         current_type=feedback_type)

@feedback_bp.route('/detail/<int:feedback_id>')
@login_required
def detail(feedback_id):
    """反馈详情"""
    feedback = get_feedback_by_id(feedback_id)
    
    if not feedback:
        flash('反馈不存在', 'error')
        return redirect(url_for('feedback.index'))
    
    # 权限检查：学生只能查看自己的反馈
    if current_user.is_student() and feedback['user_id'] != current_user.id:
        flash('您没有权限查看此反馈', 'error')
        return redirect(url_for('feedback.my_feedback'))
    
    return render_template('feedback/detail.html', feedback=feedback)

@feedback_bp.route('/update_status/<int:feedback_id>', methods=['POST'])
@teacher_or_admin_required
def update_status(feedback_id):
    """更新反馈状态（管理员功能）"""
    try:
        status = request.form.get('status')
        admin_notes = request.form.get('admin_notes', '').strip()
        
        if not status:
            return jsonify({'success': False, 'message': '状态不能为空'})
        
        # 验证状态值
        valid_statuses = ['pending', 'viewed', 'in_progress', 'resolved', 'closed']
        if status not in valid_statuses:
            return jsonify({'success': False, 'message': '无效的状态值'})
        
        success = update_feedback_status(
            feedback_id=feedback_id,
            status=status,
            admin_notes=admin_notes,
            admin_user_id=current_user.id
        )
        
        if success:
            return jsonify({'success': True, 'message': '状态更新成功'})
        else:
            return jsonify({'success': False, 'message': '反馈不存在'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@feedback_bp.route('/delete/<int:feedback_id>', methods=['POST'])
@admin_required
def delete(feedback_id):
    """删除反馈（仅管理员）"""
    try:
        success = delete_feedback(feedback_id, current_user.id)
        
        if success:
            flash('反馈删除成功', 'success')
        else:
            flash('反馈不存在', 'error')
            
    except Exception as e:
        flash(f'删除失败: {str(e)}', 'error')
    
    return redirect(url_for('feedback.admin_list'))

@feedback_bp.route('/api/statistics')
@teacher_or_admin_required
def api_statistics():
    """获取反馈统计数据API"""
    try:
        stats = get_feedback_statistics()
        return jsonify({'success': True, 'data': stats})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@feedback_bp.route('/attachment/<path:filename>')
@login_required
def download_attachment(filename):
    """下载附件"""
    try:
        # 安全检查：确保文件路径在允许的目录内
        if '..' in filename or filename.startswith('/'):
            flash('无效的文件路径', 'error')
            return redirect(url_for('feedback.index'))
        
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        
        if not os.path.exists(file_path):
            flash('文件不存在', 'error')
            return redirect(url_for('feedback.index'))
        
        from flask import send_file
        return send_file(file_path)
        
    except Exception as e:
        flash(f'下载失败: {str(e)}', 'error')
        return redirect(url_for('feedback.index'))
