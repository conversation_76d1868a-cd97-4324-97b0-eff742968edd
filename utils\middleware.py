#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中间件模块
包含访问日志记录等中间件功能
"""

import time
from flask import request, g
from flask_login import current_user
from models.database import log_access

class AccessLogMiddleware:
    """访问日志中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化中间件"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """请求前处理"""
        g.start_time = time.time()
    
    def after_request(self, response):
        """请求后处理"""
        try:
            # 计算响应时间
            response_time = None
            if hasattr(g, 'start_time'):
                response_time = (time.time() - g.start_time) * 1000  # 转换为毫秒
            
            # 获取用户信息
            user_id = None
            if current_user.is_authenticated:
                user_id = current_user.id
            
            # 获取请求信息
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
            user_agent = request.headers.get('User-Agent', '')
            request_path = request.path
            request_method = request.method
            response_status = response.status_code
            
            # 过滤掉静态资源请求
            static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf']
            if not any(request_path.endswith(ext) for ext in static_extensions):
                # 记录访问日志
                log_access(
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_path=request_path,
                    request_method=request_method,
                    response_status=response_status,
                    response_time=response_time
                )
        
        except Exception as e:
            # 记录日志失败不应该影响正常响应
            print(f"访问日志记录失败: {e}")
        
        return response
