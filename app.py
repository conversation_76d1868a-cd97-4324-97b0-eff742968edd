#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生学业数据管理与分析平台
Flask应用主文件
"""

import os
import logging
from flask import Flask, render_template, redirect, url_for, flash, session
from flask_login import Lo<PERSON><PERSON>anager, current_user
from config import config
from models.database import init_db, get_db_connection
from models.user import User
from utils.middleware import AccessLogMiddleware

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 配置日志
    if not app.debug:
        logging.basicConfig(
            level=getattr(logging, app.config['LOG_LEVEL']),
            format='%(asctime)s %(levelname)s %(name)s %(message)s',
            handlers=[
                logging.FileHandler(app.config['LOG_FILE']),
                logging.StreamHandler()
            ]
        )
    
    # 初始化数据库
    init_db(app.config['DATABASE_PATH'])

    # 初始化访问日志中间件
    AccessLogMiddleware(app)

    # 初始化Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.get_by_id(int(user_id))
    
    # 注册蓝图
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.ranking import ranking_bp
    from routes.analysis import analysis_bp
    from routes.admin import admin_bp
    from routes.api import api_bp
    from routes.feedback import feedback_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(ranking_bp, url_prefix='/ranking')
    app.register_blueprint(analysis_bp, url_prefix='/analysis')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(feedback_bp, url_prefix='/feedback')
    
    # 主页路由
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return redirect(url_for('auth.login'))
    
    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500
    
    # 上下文处理器
    @app.context_processor
    def inject_user():
        return dict(current_user=current_user)
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=False, host='0.0.0.0', port=5000)
