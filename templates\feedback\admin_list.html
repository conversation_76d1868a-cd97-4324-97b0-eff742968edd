{% extends "base.html" %}

{% block title %}反馈管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-cogs me-2"></i>反馈管理
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">首页</a></li>
                        <li class="breadcrumb-item active">反馈管理</li>
                    </ol>
                </nav>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ stats.total }}</h4>
                                    <p class="mb-0">总反馈数</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-comments fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ stats.status_stats.get('pending', 0) }}</h4>
                                    <p class="mb-0">待处理</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ stats.status_stats.get('in_progress', 0) }}</h4>
                                    <p class="mb-0">处理中</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-spinner fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ stats.recent }}</h4>
                                    <p class="mb-0">近7天新增</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">状态筛选</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>待处理</option>
                                <option value="viewed" {% if current_status == 'viewed' %}selected{% endif %}>已查看</option>
                                <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>处理中</option>
                                <option value="resolved" {% if current_status == 'resolved' %}selected{% endif %}>已解决</option>
                                <option value="closed" {% if current_status == 'closed' %}selected{% endif %}>已关闭</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">类型筛选</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">全部类型</option>
                                <option value="bug_report" {% if current_type == 'bug_report' %}selected{% endif %}>错误报告</option>
                                <option value="feature_request" {% if current_type == 'feature_request' %}selected{% endif %}>功能建议</option>
                                <option value="other" {% if current_type == 'other' %}selected{% endif %}>其他</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-filter me-2"></i>筛选
                            </button>
                            <a href="{{ url_for('feedback.admin_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>重置
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 反馈列表 -->
            {% if feedback_list %}
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>反馈记录
                        <span class="badge bg-primary ms-2">{{ pagination.total }}</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>用户</th>
                                    <th>标题</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>提交时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for feedback in feedback_list %}
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-medium">{{ feedback.full_name or feedback.username }}</div>
                                            <small class="text-muted">{{ feedback.username }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if feedback.attachment_path %}
                                            <i class="fas fa-paperclip text-muted me-2" title="包含附件"></i>
                                            {% endif %}
                                            <span class="fw-medium">{{ feedback.title }}</span>
                                        </div>
                                        {% if feedback.content|length > 50 %}
                                        <small class="text-muted d-block">{{ feedback.content[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if feedback.type == 'bug_report' %}
                                        <span class="badge bg-danger">错误报告</span>
                                        {% elif feedback.type == 'feature_request' %}
                                        <span class="badge bg-success">功能建议</span>
                                        {% else %}
                                        <span class="badge bg-secondary">其他</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <select class="form-select form-select-sm status-select" 
                                                data-feedback-id="{{ feedback.id }}"
                                                style="min-width: 100px;">
                                            <option value="pending" {% if feedback.status == 'pending' %}selected{% endif %}>待处理</option>
                                            <option value="viewed" {% if feedback.status == 'viewed' %}selected{% endif %}>已查看</option>
                                            <option value="in_progress" {% if feedback.status == 'in_progress' %}selected{% endif %}>处理中</option>
                                            <option value="resolved" {% if feedback.status == 'resolved' %}selected{% endif %}>已解决</option>
                                            <option value="closed" {% if feedback.status == 'closed' %}selected{% endif %}>已关闭</option>
                                        </select>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ feedback.created_at[:16] }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('feedback.detail', feedback_id=feedback.id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.is_admin() %}
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn"
                                                    data-feedback-id="{{ feedback.id }}"
                                                    data-feedback-title="{{ feedback.title }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            {% if pagination.total_pages > 1 %}
            <nav aria-label="反馈分页" class="mt-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page < pagination.total else pagination.total }} 
                            条，共 {{ pagination.total }} 条记录
                        </p>
                    </div>
                    <div class="col-md-6">
                        <ul class="pagination justify-content-end mb-0">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feedback.admin_list', page=pagination.page-1, status=current_status, type=current_type) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page_num in range(1, pagination.total_pages + 1) %}
                                {% if page_num == pagination.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('feedback.admin_list', page=page_num, status=current_status, type=current_type) }}">{{ page_num }}</a>
                                </li>
                                {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feedback.admin_list', page=pagination.page+1, status=current_status, type=current_type) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </nav>
            {% endif %}

            {% else %}
            <!-- 空状态 -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无反馈记录</h5>
                    <p class="text-muted">当前筛选条件下没有找到反馈记录</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除反馈 "<span id="deleteFeedbackTitle"></span>" 吗？</p>
                <p class="text-danger small">此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 状态更新处理
    document.querySelectorAll('.status-select').forEach(function(select) {
        select.addEventListener('change', function() {
            const feedbackId = this.dataset.feedbackId;
            const newStatus = this.value;
            const originalStatus = this.dataset.originalStatus || this.value;
            
            // 保存原始状态
            if (!this.dataset.originalStatus) {
                this.dataset.originalStatus = originalStatus;
            }
            
            // 发送更新请求
            fetch(`/feedback/update_status/${feedbackId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功消息
                    showAlert('状态更新成功', 'success');
                    this.dataset.originalStatus = newStatus;
                } else {
                    // 恢复原始状态
                    this.value = originalStatus;
                    showAlert(data.message || '状态更新失败', 'error');
                }
            })
            .catch(error => {
                // 恢复原始状态
                this.value = originalStatus;
                showAlert('网络错误，请重试', 'error');
            });
        });
    });
    
    // 删除按钮处理
    document.querySelectorAll('.delete-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const feedbackId = this.dataset.feedbackId;
            const feedbackTitle = this.dataset.feedbackTitle;
            
            document.getElementById('deleteFeedbackTitle').textContent = feedbackTitle;
            document.getElementById('deleteForm').action = `/feedback/delete/${feedbackId}`;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
    
    // 显示提示消息
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);
        
        // 3秒后自动消失
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    }
});
</script>
{% endblock %}
