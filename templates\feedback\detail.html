{% extends "base.html" %}

{% block title %}反馈详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-comment-dots me-2"></i>反馈详情
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">首页</a></li>
                        {% if current_user.is_admin() or current_user.is_teacher() %}
                        <li class="breadcrumb-item"><a href="{{ url_for('feedback.admin_list') }}">反馈管理</a></li>
                        {% else %}
                        <li class="breadcrumb-item"><a href="{{ url_for('feedback.my_feedback') }}">我的反馈</a></li>
                        {% endif %}
                        <li class="breadcrumb-item active">反馈详情</li>
                    </ol>
                </nav>
            </div>

            <div class="row">
                <!-- 反馈详情 -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>{{ feedback.title }}
                                </h5>
                                <div>
                                    {% if feedback.type == 'bug_report' %}
                                    <span class="badge bg-danger">错误报告</span>
                                    {% elif feedback.type == 'feature_request' %}
                                    <span class="badge bg-success">功能建议</span>
                                    {% else %}
                                    <span class="badge bg-secondary">其他</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 反馈内容 -->
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">反馈内容：</h6>
                                <div class="border rounded p-3 bg-light">
                                    {{ feedback.content | replace('\n', '<br>') | safe }}
                                </div>
                            </div>

                            <!-- 附件 -->
                            {% if feedback.attachment_path %}
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">附件：</h6>
                                <div class="border rounded p-3">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-paperclip text-primary me-2"></i>
                                        <a href="{{ url_for('feedback.download_attachment', filename=feedback.attachment_path) }}" 
                                           class="text-decoration-none" target="_blank">
                                            查看附件
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 管理员备注 -->
                            {% if feedback.admin_notes %}
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">处理备注：</h6>
                                <div class="border rounded p-3 bg-info bg-opacity-10">
                                    <i class="fas fa-user-shield text-info me-2"></i>
                                    {{ feedback.admin_notes | replace('\n', '<br>') | safe }}
                                </div>
                            </div>
                            {% endif %}

                            <!-- 操作按钮 -->
                            <div class="d-flex justify-content-between">
                                {% if current_user.is_admin() or current_user.is_teacher() %}
                                <a href="{{ url_for('feedback.admin_list') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回列表
                                </a>
                                {% else %}
                                <a href="{{ url_for('feedback.my_feedback') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回我的反馈
                                </a>
                                {% endif %}
                                
                                {% if current_user.is_admin() or current_user.is_teacher() %}
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateModal">
                                    <i class="fas fa-edit me-2"></i>处理反馈
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 侧边栏信息 -->
                <div class="col-lg-4">
                    <!-- 基本信息 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info me-2"></i>基本信息
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-4 text-muted">提交人：</div>
                                <div class="col-8">{{ feedback.full_name or feedback.username }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">用户名：</div>
                                <div class="col-8">{{ feedback.username }}</div>
                            </div>
                            {% if feedback.email %}
                            <div class="row mb-2">
                                <div class="col-4 text-muted">邮箱：</div>
                                <div class="col-8">{{ feedback.email }}</div>
                            </div>
                            {% endif %}
                            <div class="row mb-2">
                                <div class="col-4 text-muted">状态：</div>
                                <div class="col-8">
                                    {% if feedback.status == 'pending' %}
                                    <span class="badge bg-warning">待处理</span>
                                    {% elif feedback.status == 'viewed' %}
                                    <span class="badge bg-info">已查看</span>
                                    {% elif feedback.status == 'in_progress' %}
                                    <span class="badge bg-primary">处理中</span>
                                    {% elif feedback.status == 'resolved' %}
                                    <span class="badge bg-success">已解决</span>
                                    {% else %}
                                    <span class="badge bg-secondary">已关闭</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">提交时间：</div>
                                <div class="col-8">
                                    <small>{{ feedback.created_at }}</small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4 text-muted">更新时间：</div>
                                <div class="col-8">
                                    <small>{{ feedback.updated_at }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作历史 -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>状态历史
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">反馈提交</h6>
                                        <small class="text-muted">{{ feedback.created_at }}</small>
                                    </div>
                                </div>
                                {% if feedback.status != 'pending' %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">状态更新</h6>
                                        <small class="text-muted">{{ feedback.updated_at }}</small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理反馈模态框 -->
{% if current_user.is_admin() or current_user.is_teacher() %}
<div class="modal fade" id="updateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">处理反馈</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('feedback.update_status', feedback_id=feedback.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">状态 <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="pending" {% if feedback.status == 'pending' %}selected{% endif %}>待处理</option>
                            <option value="viewed" {% if feedback.status == 'viewed' %}selected{% endif %}>已查看</option>
                            <option value="in_progress" {% if feedback.status == 'in_progress' %}selected{% endif %}>处理中</option>
                            <option value="resolved" {% if feedback.status == 'resolved' %}selected{% endif %}>已解决</option>
                            <option value="closed" {% if feedback.status == 'closed' %}selected{% endif %}>已关闭</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">处理备注</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="4" 
                                  placeholder="请输入处理备注...">{{ feedback.admin_notes or '' }}</textarea>
                        <div class="form-text">记录处理过程、解决方案或其他相关信息</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更新</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}
</style>
{% endblock %}
