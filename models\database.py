#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
扩展现有数据库，添加用户管理、日志等功能
"""

import sqlite3
import logging
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

def get_db_connection(db_path='scholarship_data.db'):
    """获取数据库连接"""
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
    return conn

@contextmanager
def get_db_cursor(db_path='scholarship_data.db'):
    """数据库游标上下文管理器"""
    conn = get_db_connection(db_path)
    try:
        cursor = conn.cursor()
        yield cursor
        conn.commit()
    except Exception as e:
        conn.rollback()
        raise e
    finally:
        conn.close()

def init_db(db_path='scholarship_data.db'):
    """初始化数据库，创建新增的表"""
    conn = get_db_connection(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                email TEXT,
                full_name TEXT,
                role TEXT NOT NULL DEFAULT 'student',  -- admin, teacher, student
                student_id TEXT,  -- 关联学生学号（仅学生用户）
                is_active BOOLEAN DEFAULT 1,
                is_sub_admin BOOLEAN DEFAULT 0,  -- 教师是否为子管理员
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # 创建系统日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                description TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 创建API配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key TEXT NOT NULL UNIQUE,
                config_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建用户反馈表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                type TEXT NOT NULL DEFAULT 'other',  -- bug_report, feature_request, other
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                status TEXT DEFAULT 'pending',  -- pending, viewed, in_progress, resolved, closed
                admin_notes TEXT,  -- 管理员处理备注
                attachment_path TEXT,  -- 附件路径（截图等）
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # 检查并添加新字段（用于数据库迁移）
        try:
            # 检查是否存在type字段
            cursor.execute("PRAGMA table_info(feedback)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'type' not in columns:
                cursor.execute('ALTER TABLE feedback ADD COLUMN type TEXT NOT NULL DEFAULT "other"')

            if 'admin_notes' not in columns:
                cursor.execute('ALTER TABLE feedback ADD COLUMN admin_notes TEXT')

            if 'attachment_path' not in columns:
                cursor.execute('ALTER TABLE feedback ADD COLUMN attachment_path TEXT')

            # 如果存在旧的admin_reply字段，将其重命名为admin_notes
            if 'admin_reply' in columns and 'admin_notes' not in columns:
                # SQLite不支持直接重命名列，需要重建表
                cursor.execute('''
                    CREATE TABLE feedback_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        type TEXT NOT NULL DEFAULT 'other',
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        admin_notes TEXT,
                        attachment_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                cursor.execute('''
                    INSERT INTO feedback_new (id, user_id, title, content, status, admin_notes, created_at, updated_at)
                    SELECT id, user_id, title, content, status, admin_reply, created_at, updated_at
                    FROM feedback
                ''')

                cursor.execute('DROP TABLE feedback')
                cursor.execute('ALTER TABLE feedback_new RENAME TO feedback')

        except Exception as e:
            logger.warning(f"反馈表迁移警告: {e}")

        # 为反馈表创建索引以提高查询性能
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON feedback (user_id)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_feedback_status ON feedback (status)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_feedback_type ON feedback (type)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON feedback (created_at)
        ''')
        
        # 创建数据导入记录表（扩展现有的file_records）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS import_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                operation_type TEXT NOT NULL,  -- import, export, delete
                record_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'success',  -- success, failed, partial
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # 创建访问日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS access_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                ip_address TEXT,
                user_agent TEXT,
                request_path TEXT NOT NULL,
                request_method TEXT NOT NULL DEFAULT 'GET',
                response_status INTEGER,
                response_time REAL,  -- 响应时间（毫秒）
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # 为访问日志表创建索引以提高查询性能
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_access_logs_created_at ON access_logs (created_at)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_access_logs_user_id ON access_logs (user_id)
        ''')
        
        # 插入默认API配置
        cursor.execute('''
            INSERT OR IGNORE INTO api_configs (config_key, config_value, description) VALUES
            ('deepseek_api_key', '***********************************', 'DeepSeek API密钥'),
            ('deepseek_api_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1', '阿里云百炼API端点'),
            ('deepseek_model', 'deepseek-r1', 'DeepSeek模型名称'),
            ('ai_temperature', '0.7', 'AI温度系数'),
            ('ai_max_tokens', '2000', 'AI最大输出token数'),
            ('analysis_prompt', '请分析该学生的学业表现，包括成绩趋势、排名变化、优势科目等方面。', '学生分析Prompt'),
            ('sql_prompt', '你是一个SQL专家，请根据用户的自然语言描述生成对应的SQL查询语句。', 'SQL生成Prompt')
        ''')
        
        # 创建默认管理员用户（如果不存在）
        from werkzeug.security import generate_password_hash
        admin_password_hash = generate_password_hash('admin123')
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role, is_active) 
            VALUES ('admin', ?, '系统管理员', 'admin', 1)
        ''', (admin_password_hash,))
        
        conn.commit()
        logger.info("数据库初始化完成")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"数据库初始化失败: {e}")
        raise e
    finally:
        conn.close()

def log_user_action(user_id, action, description=None, ip_address=None, user_agent=None, db_path='scholarship_data.db'):
    """记录用户操作日志"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT INTO system_logs (user_id, action, description, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, action, description, ip_address, user_agent))
    except Exception as e:
        logger.error(f"记录用户操作日志失败: {e}")

def get_api_config(key, default=None, db_path='scholarship_data.db'):
    """获取API配置"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('SELECT config_value FROM api_configs WHERE config_key = ?', (key,))
            result = cursor.fetchone()
            return result['config_value'] if result else default
    except Exception as e:
        logger.error(f"获取API配置失败: {e}")
        return default

def set_api_config(key, value, description=None, db_path='scholarship_data.db'):
    """设置API配置"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT OR REPLACE INTO api_configs (config_key, config_value, description, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, description))
    except Exception as e:
        logger.error(f"设置API配置失败: {e}")
        raise e

def log_access(user_id=None, ip_address=None, user_agent=None, request_path=None,
               request_method='GET', response_status=200, response_time=None, db_path='scholarship_data.db'):
    """记录访问日志"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT INTO access_logs (user_id, ip_address, user_agent, request_path,
                                       request_method, response_status, response_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, ip_address, user_agent, request_path, request_method, response_status, response_time))
    except Exception as e:
        logger.error(f"记录访问日志失败: {e}")

def get_access_stats(days=7, db_path='scholarship_data.db'):
    """获取访问统计数据"""
    try:
        with get_db_cursor(db_path) as cursor:
            # 获取近N天的每日访问统计
            cursor.execute('''
                SELECT
                    DATE(created_at) as access_date,
                    COUNT(*) as access_count,
                    COUNT(DISTINCT user_id) as unique_users
                FROM access_logs
                WHERE created_at >= datetime('now', '-{} days')
                GROUP BY DATE(created_at)
                ORDER BY access_date
            '''.format(days))

            daily_stats = [dict(row) for row in cursor.fetchall()]

            # 获取总访问量
            cursor.execute('''
                SELECT COUNT(*) as total_access
                FROM access_logs
                WHERE created_at >= datetime('now', '-{} days')
            '''.format(days))

            total_access = cursor.fetchone()['total_access']

            return {
                'daily_stats': daily_stats,
                'total_access': total_access,
                'days': days
            }
    except Exception as e:
        logger.error(f"获取访问统计失败: {e}")
        return {'daily_stats': [], 'total_access': 0, 'days': days}

# 反馈管理相关函数
def create_feedback(user_id, feedback_type, title, content, attachment_path=None, db_path='scholarship_data.db'):
    """创建新反馈"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT INTO feedback (user_id, type, title, content, attachment_path)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, feedback_type, title, content, attachment_path))
            feedback_id = cursor.lastrowid

            # 记录操作日志
            log_user_action(user_id, 'feedback_created', f'提交反馈: {title}')

            return feedback_id
    except Exception as e:
        logger.error(f"创建反馈失败: {e}")
        raise e

def get_feedback_list(user_id=None, status=None, feedback_type=None, page=1, per_page=20, db_path='scholarship_data.db'):
    """获取反馈列表（支持分页和筛选）"""
    try:
        with get_db_cursor(db_path) as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if user_id is not None:
                where_conditions.append('f.user_id = ?')
                params.append(user_id)

            if status:
                where_conditions.append('f.status = ?')
                params.append(status)

            if feedback_type:
                where_conditions.append('f.type = ?')
                params.append(feedback_type)

            where_clause = 'WHERE ' + ' AND '.join(where_conditions) if where_conditions else ''

            # 获取总数
            count_query = f'''
                SELECT COUNT(*) as total
                FROM feedback f
                {where_clause}
            '''
            cursor.execute(count_query, params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            offset = (page - 1) * per_page
            data_query = f'''
                SELECT
                    f.id, f.user_id, f.type, f.title, f.content, f.status,
                    f.admin_notes, f.attachment_path, f.created_at, f.updated_at,
                    u.username, u.full_name
                FROM feedback f
                LEFT JOIN users u ON f.user_id = u.id
                {where_clause}
                ORDER BY f.created_at DESC
                LIMIT ? OFFSET ?
            '''
            cursor.execute(data_query, params + [per_page, offset])
            feedback_list = [dict(row) for row in cursor.fetchall()]

            # 计算分页信息
            total_pages = (total + per_page - 1) // per_page
            has_prev = page > 1
            has_next = page < total_pages

            return {
                'feedback_list': feedback_list,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'total_pages': total_pages,
                    'has_prev': has_prev,
                    'has_next': has_next
                }
            }
    except Exception as e:
        logger.error(f"获取反馈列表失败: {e}")
        return {'feedback_list': [], 'pagination': {'page': 1, 'per_page': per_page, 'total': 0, 'total_pages': 0, 'has_prev': False, 'has_next': False}}

def get_feedback_by_id(feedback_id, db_path='scholarship_data.db'):
    """根据ID获取反馈详情"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                SELECT
                    f.id, f.user_id, f.type, f.title, f.content, f.status,
                    f.admin_notes, f.attachment_path, f.created_at, f.updated_at,
                    u.username, u.full_name, u.email
                FROM feedback f
                LEFT JOIN users u ON f.user_id = u.id
                WHERE f.id = ?
            ''', (feedback_id,))
            result = cursor.fetchone()
            return dict(result) if result else None
    except Exception as e:
        logger.error(f"获取反馈详情失败: {e}")
        return None

def update_feedback_status(feedback_id, status, admin_notes=None, admin_user_id=None, db_path='scholarship_data.db'):
    """更新反馈状态"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                UPDATE feedback
                SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, admin_notes, feedback_id))

            # 记录操作日志
            if admin_user_id:
                log_user_action(admin_user_id, 'feedback_updated', f'更新反馈状态: {feedback_id} -> {status}')

            return cursor.rowcount > 0
    except Exception as e:
        logger.error(f"更新反馈状态失败: {e}")
        raise e

def delete_feedback(feedback_id, admin_user_id=None, db_path='scholarship_data.db'):
    """删除反馈（仅管理员）"""
    try:
        with get_db_cursor(db_path) as cursor:
            # 先获取反馈信息用于日志
            cursor.execute('SELECT title FROM feedback WHERE id = ?', (feedback_id,))
            feedback = cursor.fetchone()

            if feedback:
                cursor.execute('DELETE FROM feedback WHERE id = ?', (feedback_id,))

                # 记录操作日志
                if admin_user_id:
                    log_user_action(admin_user_id, 'feedback_deleted', f'删除反馈: {feedback["title"]}')

                return cursor.rowcount > 0
            return False
    except Exception as e:
        logger.error(f"删除反馈失败: {e}")
        raise e

def get_feedback_statistics(db_path='scholarship_data.db'):
    """获取反馈统计信息"""
    try:
        with get_db_cursor(db_path) as cursor:
            # 按状态统计
            cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM feedback
                GROUP BY status
            ''')
            status_stats = {row['status']: row['count'] for row in cursor.fetchall()}

            # 按类型统计
            cursor.execute('''
                SELECT type, COUNT(*) as count
                FROM feedback
                GROUP BY type
            ''')
            type_stats = {row['type']: row['count'] for row in cursor.fetchall()}

            # 总数统计
            cursor.execute('SELECT COUNT(*) as total FROM feedback')
            total = cursor.fetchone()['total']

            # 近7天新增反馈
            cursor.execute('''
                SELECT COUNT(*) as recent
                FROM feedback
                WHERE created_at >= datetime('now', '-7 days')
            ''')
            recent = cursor.fetchone()['recent']

            return {
                'status_stats': status_stats,
                'type_stats': type_stats,
                'total': total,
                'recent': recent
            }
    except Exception as e:
        logger.error(f"获取反馈统计失败: {e}")
        return {'status_stats': {}, 'type_stats': {}, 'total': 0, 'recent': 0}
    except Exception as e:
        logger.error(f"获取访问统计失败: {e}")
        return {
            'daily_stats': [],
            'total_access': 0,
            'days': days
        }
